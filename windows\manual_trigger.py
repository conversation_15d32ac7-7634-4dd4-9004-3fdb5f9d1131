import time
import requests
import win32gui
import threading
import msvcrt

# 配置
WINDOW_KEYWORD = "消息"
DINGDING_WEBHOOK = "https://oapi.dingtalk.com/robot/send?access_token=ee5b869e1a3c55cc799af160c17a9ec3f76e9073eb98ab83c35ac5ddf128e527"
SECURITY_KEYWORD = "来消息了"

def send_dingding_msg():
    """发送钉钉消息"""
    msg = f"{SECURITY_KEYWORD}：检测到{WINDOW_KEYWORD}有新消息，请及时查看电脑。"
    print(f"🚀 发送钉钉消息: {msg}")
    try:
        response = requests.post(DINGDING_WEBHOOK, json={
            "msgtype": "text",
            "text": {"content": msg}
        }, timeout=5)
        
        if response.status_code == 200 and response.json().get("errcode") == 0:
            print("✅ 钉钉消息发送成功")
            return True
        else:
            print(f"❌ 钉钉消息发送失败: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 发送钉钉消息时出错: {e}")
        return False

def get_target_window():
    """获取目标窗口"""
    def enum_handler(hwnd, result):
        try:
            if win32gui.IsWindowVisible(hwnd):
                title = win32gui.GetWindowText(hwnd)
                if title == WINDOW_KEYWORD:
                    result.append({
                        'hwnd': hwnd,
                        'title': title,
                        'is_minimized': win32gui.IsIconic(hwnd),
                        'is_foreground': win32gui.GetForegroundWindow() == hwnd
                    })
        except:
            pass
    
    windows = []
    win32gui.EnumWindows(enum_handler, windows)
    return windows[0] if windows else None

def monitor_keyboard():
    """监控键盘输入"""
    print("📱 手动触发模式:")
    print("  - 按 SPACE 键：手动触发通知测试")
    print("  - 按 ESC 键：退出程序")
    print("  - 按 S 键：显示当前窗口状态")
    print()
    
    while True:
        if msvcrt.kbhit():
            key = msvcrt.getch()
            
            if key == b' ':  # 空格键
                print("\n🚨 手动触发通知测试...")
                window = get_target_window()
                if window:
                    if window['is_minimized']:
                        print(f"✅ 窗口状态正常: {window['title']} (最小化)")
                        send_dingding_msg()
                    else:
                        print(f"⚠️  窗口未最小化: {window['title']}")
                        print("   请先最小化闲管家窗口")
                else:
                    print("❌ 未找到'消息'窗口")
                print()
                
            elif key == b's' or key == b'S':  # S键
                print("\n📊 当前窗口状态:")
                window = get_target_window()
                if window:
                    status = "最小化" if window['is_minimized'] else "显示中"
                    foreground = "前台" if window['is_foreground'] else "后台"
                    print(f"  窗口: {window['title']}")
                    print(f"  状态: {status}, {foreground}")
                    print(f"  句柄: {window['hwnd']}")
                else:
                    print("  ❌ 未找到'消息'窗口")
                print()
                
            elif key == b'\x1b':  # ESC键
                print("\n👋 退出程序")
                break
                
        time.sleep(0.1)

if __name__ == "__main__":
    print("🎯 手动触发测试版本")
    print("=" * 50)
    print("🎯 用途：手动测试通知功能")
    print("💡 当你收到新消息时，按空格键手动触发通知")
    print("=" * 50)
    
    # 检查初始状态
    window = get_target_window()
    if window:
        status = "最小化" if window['is_minimized'] else "显示中"
        print(f"✅ 找到目标窗口: {window['title']} ({status})")
    else:
        print("❌ 未找到'消息'窗口，请确保闲管家正在运行")
    
    print()
    
    try:
        monitor_keyboard()
    except KeyboardInterrupt:
        print("\n✅ 程序已停止")
