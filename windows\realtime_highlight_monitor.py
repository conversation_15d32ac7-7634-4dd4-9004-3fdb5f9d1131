import time
import requests
import win32gui
import win32con
import win32api
import ctypes
from ctypes import wintypes
from datetime import datetime
import threading
import sounddevice as sd
import numpy as np
from collections import deque

# 配置
WINDOW_KEYWORD = "消息"
DINGDING_WEBHOOK = "https://oapi.dingtalk.com/robot/send?access_token=ee5b869e1a3c55cc799af160c17a9ec3f76e9073eb98ab83c35ac5ddf128e527"
SECURITY_KEYWORD = "来消息了"

CHECK_INTERVAL = 3  # 3秒检查一次，实时性更好
ALERT_INTERVAL = 10  # 5分钟防抖

# 全局变量
last_alert_time = 0
last_highlight_state = False
sound_events = deque(maxlen=100)
sound_monitoring = True

def send_dingding_msg(trigger_reasons):
    """发送钉钉消息 - 多条件检测入口"""
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    reasons_text = "、".join(trigger_reasons)
    msg = f"{SECURITY_KEYWORD}：{current_time} 检测到{WINDOW_KEYWORD}有新消息({reasons_text})，请及时查看电脑。"
    print(f"🚀 发送钉钉消息: {msg}")
    try:
        response = requests.post(DINGDING_WEBHOOK, json={
            "msgtype": "text",
            "text": {"content": msg}
        }, timeout=5)

        if response.status_code == 200 and response.json().get("errcode") == 0:
            print("✅ 钉钉消息发送成功")
            return True
        else:
            print(f"❌ 钉钉消息发送失败: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 发送钉钉消息时出错: {e}")
        return False

def is_screen_locked():
    """检查屏幕是否锁定"""
    try:
        user32 = ctypes.windll.user32
        # 检查是否有屏保或锁屏
        screensaver_running = user32.SystemParametersInfoW(0x0072, 0, None, 0)  # SPI_GETSCREENSAVERRUNNING

        # 检查工作站是否锁定
        hdesk = user32.OpenInputDesktop(0, False, 0x0100)  # DESKTOP_SWITCHDESKTOP
        if hdesk == 0:
            return True  # 无法访问输入桌面，可能锁屏了
        user32.CloseDesktop(hdesk)

        return False
    except:
        return False

def get_target_window():
    """获取目标窗口"""
    def enum_handler(hwnd, result):
        try:
            if win32gui.IsWindowVisible(hwnd):
                title = win32gui.GetWindowText(hwnd)
                if title == WINDOW_KEYWORD:
                    # 获取更详细的窗口状态
                    is_minimized = win32gui.IsIconic(hwnd)
                    is_foreground = win32gui.GetForegroundWindow() == hwnd

                    # 检查窗口是否真的在任务栏中（而不是因为锁屏导致的误判）
                    window_state = "unknown"
                    if is_minimized:
                        window_state = "minimized"
                    elif is_foreground:
                        window_state = "foreground"
                    else:
                        window_state = "background"

                    result.append({
                        'hwnd': hwnd,
                        'title': title,
                        'is_minimized': is_minimized,
                        'is_foreground': is_foreground,
                        'window_state': window_state
                    })
        except:
            pass

    windows = []
    win32gui.EnumWindows(enum_handler, windows)
    return windows[0] if windows else None

def sound_monitor_thread():
    """声音监控线程"""
    global sound_events, sound_monitoring

    def audio_callback(indata, frames, time_info, status):
        if sound_monitoring:
            volume = np.linalg.norm(indata) / len(indata)
            current_time = time.time()

            sound_events.append({
                'time': current_time,
                'volume': volume
            })

            # 只打印较大的声音
            if volume > 0.008:
                print(f"🔊 声音事件: 音量={volume:.4f}")

    try:
        print("🎧 声音监控已启动")
        with sd.InputStream(callback=audio_callback, channels=1, samplerate=44100, blocksize=1024):
            while sound_monitoring:
                time.sleep(0.1)
    except Exception as e:
        print(f"❌ 声音监控异常: {e}")

def check_sound_activity(seconds=8):
    """检查声音活动"""
    if not sound_events:
        return False, "无声音数据"

    current_time = time.time()
    recent_sounds = [e for e in sound_events if current_time - e['time'] < seconds]

    if not recent_sounds:
        return False, f"最近{seconds}秒无声音"

    # 检查是否有明显的声音事件
    significant_sounds = [e for e in recent_sounds if e['volume'] > 0.015]

    if significant_sounds:
        max_vol = max(e['volume'] for e in significant_sounds)
        return True, f"检测到{len(significant_sounds)}个显著声音事件(最大音量{max_vol:.4f})"

    return False, f"最近{seconds}秒内{len(recent_sounds)}个声音事件，但都较小"

def check_taskbar_button_highlight(hwnd):
    """检查任务栏按钮是否高亮 - 改进版本"""
    try:
        user32 = ctypes.windll.user32

        # 方法1: 检查窗口的闪烁状态（更准确的方法）
        class FLASHWINFO(ctypes.Structure):
            _fields_ = [
                ('cbSize', wintypes.UINT),
                ('hwnd', wintypes.HWND),
                ('dwFlags', wintypes.DWORD),
                ('uCount', wintypes.UINT),
                ('dwTimeout', wintypes.DWORD)
            ]

        flash_info = FLASHWINFO()
        flash_info.cbSize = ctypes.sizeof(FLASHWINFO)
        flash_info.hwnd = hwnd
        flash_info.dwFlags = 0x0C  # FLASHW_ALL - 检查所有闪烁状态
        flash_info.uCount = 0
        flash_info.dwTimeout = 0

        # 检查是否正在闪烁
        result = user32.FlashWindowEx(ctypes.byref(flash_info))
        if result:
            return True, "检测到窗口正在闪烁"

        # 方法2: 检查窗口是否有未读消息的视觉指示
        # 获取窗口信息
        class WINDOWINFO(ctypes.Structure):
            _fields_ = [
                ('cbSize', wintypes.DWORD),
                ('rcWindow', wintypes.RECT),
                ('rcClient', wintypes.RECT),
                ('dwStyle', wintypes.DWORD),
                ('dwExStyle', wintypes.DWORD),
                ('dwWindowStatus', wintypes.DWORD),
                ('cxWindowBorders', wintypes.UINT),
                ('cyWindowBorders', wintypes.UINT),
                ('atomWindowType', wintypes.ATOM),
                ('wCreatorVersion', wintypes.WORD)
            ]

        window_info = WINDOWINFO()
        window_info.cbSize = ctypes.sizeof(WINDOWINFO)

        if user32.GetWindowInfo(hwnd, ctypes.byref(window_info)):
            # 检查窗口状态 - 只有真正激活的窗口才算高亮
            if window_info.dwWindowStatus == 1:  # 窗口激活状态
                is_foreground = win32gui.GetForegroundWindow() == hwnd
                if not is_foreground:  # 不在前台但激活，可能是高亮
                    return True, "窗口激活但不在前台"

        return False, "未检测到真正的高亮状态"

    except Exception as e:
        return False, f"检测异常: {e}"

def advanced_highlight_detection(hwnd):
    """高级高亮检测 - 更严格的判断"""
    try:
        # 方法1: 检查窗口标题是否包含未读指示
        title = win32gui.GetWindowText(hwnd)
        import re

        # 检查标题中是否有数字（可能是未读消息数）
        numbers = re.findall(r'\d+', title)
        if numbers:
            return True, f"标题包含数字: {title}"

        # 检查标题中是否有括号或其他指示符
        indicators = ['(', ')', '[', ']', '•', '●', '★', '!', '*', '未读', '新消息']
        if any(indicator in title for indicator in indicators):
            return True, f"标题包含指示符: {title}"

        # 方法2: 检查窗口类名和进程信息
        try:
            class_name = win32gui.GetClassName(hwnd)
            if "flash" in class_name.lower() or "highlight" in class_name.lower():
                return True, f"窗口类名包含高亮指示: {class_name}"
        except:
            pass

        # 方法3: 更严格的状态检查 - 移除过于宽泛的判断
        # 不再仅仅因为"最小化但可见"就判断为高亮
        # 需要更明确的证据

        return False, "未检测到明确的高亮指示"

    except Exception as e:
        return False, f"高级检测异常: {e}"

def check_taskbar_icon_state(hwnd):
    """检查任务栏图标的真实状态 - 改进版本"""
    try:
        user32 = ctypes.windll.user32

        # 方法1: 检查窗口的Flash状态（最直接的方法）
        class FLASHWINFO(ctypes.Structure):
            _fields_ = [
                ('cbSize', wintypes.UINT),
                ('hwnd', wintypes.HWND),
                ('dwFlags', wintypes.DWORD),
                ('uCount', wintypes.UINT),
                ('dwTimeout', wintypes.DWORD)
            ]

        # 先检查当前是否在闪烁
        flash_info = FLASHWINFO()
        flash_info.cbSize = ctypes.sizeof(FLASHWINFO)
        flash_info.hwnd = hwnd
        flash_info.dwFlags = 0  # FLASHW_STOP - 停止闪烁并返回之前的状态

        result = user32.FlashWindowEx(ctypes.byref(flash_info))
        if result:
            return True, "任务栏图标正在闪烁或高亮"

        # 方法2: 检查窗口的激活状态和可见性
        is_minimized = win32gui.IsIconic(hwnd)
        is_visible = win32gui.IsWindowVisible(hwnd)
        is_foreground = win32gui.GetForegroundWindow() == hwnd

        # 如果窗口最小化但可见，且不在前台，很可能是高亮状态
        if is_minimized and is_visible and not is_foreground:
            # 进一步检查窗口属性
            try:
                # 获取窗口的扩展样式
                ex_style = win32gui.GetWindowLong(hwnd, win32con.GWL_EXSTYLE)
                # 检查是否有特殊的显示状态
                if ex_style & 0x80000:  # WS_EX_LAYERED - 可能表示有特殊效果
                    return True, "窗口有特殊显示效果（可能高亮）"
            except:
                pass

            return True, "窗口最小化但保持可见（可能高亮）"

        # 方法3: 检查窗口是否有未处理的输入
        msg_count = user32.GetQueueStatus(0x1FF)  # QS_ALLINPUT
        if msg_count > 0:
            return True, f"窗口有未处理消息: {msg_count}"

        return False, "任务栏图标状态正常"

    except Exception as e:
        return False, f"任务栏状态检测异常: {e}"

def comprehensive_detection(hwnd):
    """综合检测方法 - 多条件检测入口"""
    trigger_reasons = []
    detection_details = []

    # 1. 改进的高亮状态检测
    basic_result, basic_reason = check_taskbar_button_highlight(hwnd)
    advanced_result, advanced_reason = advanced_highlight_detection(hwnd)
    taskbar_result, taskbar_reason = check_taskbar_icon_state(hwnd)

    # 只有多个检测方法都确认才认为是高亮（提高准确性）
    highlight_confirmed = (basic_result and advanced_result) or taskbar_result
    if highlight_confirmed:
        trigger_reasons.append("图标高亮")

    detection_details.append(f"{'✅' if basic_result else '❌'} 基础检测: {basic_reason}")
    detection_details.append(f"{'✅' if advanced_result else '❌'} 高级检测: {advanced_reason}")
    detection_details.append(f"{'✅' if taskbar_result else '❌'} 任务栏检测: {taskbar_reason}")

    # 2. 声音活动检测
    sound_result, sound_reason = check_sound_activity(8)
    if sound_result:
        trigger_reasons.append("声音活动")
    detection_details.append(f"{'✅' if sound_result else '❌'} 声音检测: {sound_reason}")

    # 3. 窗口标题检测
    title_result, title_reason = check_window_title_changes(hwnd)
    if title_result:
        trigger_reasons.append("标题变化")
    detection_details.append(f"{'✅' if title_result else '❌'} 标题检测: {title_reason}")

    # 4. 窗口闪烁检测
    flash_result, flash_reason = check_window_flashing(hwnd)
    if flash_result:
        trigger_reasons.append("窗口闪烁")
    detection_details.append(f"{'✅' if flash_result else '❌'} 闪烁检测: {flash_reason}")

    # 综合判断
    should_trigger = len(trigger_reasons) > 0
    details_text = "\n    ".join(detection_details)

    return should_trigger, trigger_reasons, details_text

def check_window_title_changes(hwnd):
    """检查窗口标题是否包含未读消息指示"""
    try:
        title = win32gui.GetWindowText(hwnd)

        # 检查标题中是否有数字或特殊符号
        import re
        has_numbers = bool(re.search(r'\d+', title))
        has_brackets = any(char in title for char in '()[]{}')
        has_symbols = any(char in title for char in '•●★!@#*')

        if has_numbers or has_brackets or has_symbols:
            return True, f"标题包含指示符: {title}"

        return False, f"标题正常: {title}"
    except Exception as e:
        return False, f"标题检测异常: {e}"

def check_window_flashing(hwnd):
    """检查窗口闪烁状态 - 改进版本"""
    try:
        user32 = ctypes.windll.user32

        # 方法1: 检查窗口是否正在主动闪烁
        # 先尝试停止闪烁，如果返回True说明之前在闪烁
        class FLASHWINFO(ctypes.Structure):
            _fields_ = [
                ('cbSize', wintypes.UINT),
                ('hwnd', wintypes.HWND),
                ('dwFlags', wintypes.DWORD),
                ('uCount', wintypes.UINT),
                ('dwTimeout', wintypes.DWORD)
            ]

        flash_info = FLASHWINFO()
        flash_info.cbSize = ctypes.sizeof(FLASHWINFO)
        flash_info.hwnd = hwnd
        flash_info.dwFlags = 0  # FLASHW_STOP
        flash_info.uCount = 0
        flash_info.dwTimeout = 0

        # 检查是否正在闪烁
        result = user32.FlashWindowEx(ctypes.byref(flash_info))

        # 方法2: 更严格的验证 - 检查窗口是否真的需要用户注意
        # 只有在特定条件下才认为是真正的闪烁
        if result:
            # 验证窗口确实需要注意（例如不在前台但有活动）
            is_foreground = win32gui.GetForegroundWindow() == hwnd
            is_minimized = win32gui.IsIconic(hwnd)

            if not is_foreground and is_minimized:
                return True, "检测到窗口闪烁（最小化且需要注意）"
            else:
                return False, "窗口状态正常，非真实闪烁"

        return False, "窗口未闪烁"
    except Exception as e:
        return False, f"闪烁检测异常: {e}"

if __name__ == "__main__":
    print("🔥 多条件检测版本 - 闲管家消息监控")
    print("=" * 60)
    print(f"检查间隔: {CHECK_INTERVAL}秒 (实时检测)")
    print(f"防抖间隔: {ALERT_INTERVAL//60}分钟")
    print("=" * 60)
    print("🎯 检测策略 (符合任一条件即触发):")
    print("  1. 任务栏图标高亮状态")
    print("  2. 声音活动检测")
    print("  3. 窗口标题变化")
    print("  4. 窗口闪烁检测")
    print("  5. 窗口非前台时监控（包括最小化、后台、锁屏）")
    print("按 Ctrl+C 退出...")
    print("=" * 60)

    # 启动声音监控线程
    sound_thread = threading.Thread(target=sound_monitor_thread, daemon=True)
    sound_thread.start()

    # 检查初始状态
    window = get_target_window()
    if window:
        status = "最小化" if window['is_minimized'] else "显示中"
        print(f"✅ 找到目标窗口: {window['title']} ({status})")

        if window['is_minimized']:
            should_trigger, trigger_reasons, details = comprehensive_detection(window['hwnd'])
            print(f"🔍 初始检测结果: {'触发' if should_trigger else '正常'}")
            if trigger_reasons:
                print(f"    触发原因: {', '.join(trigger_reasons)}")
            print(f"    {details}")
        else:
            print("👁️  窗口当前显示中，暂不检测")
    else:
        print("❌ 未找到'消息'窗口，请确保闲管家正在运行")

    print()
    print("🔍 开始多条件实时监控...")

    try:
        cycle = 0
        while True:
            cycle += 1
            current_time = time.time()
            time_str = datetime.now().strftime("%H:%M:%S")

            # 检查屏幕锁定状态
            screen_locked = is_screen_locked()
            if screen_locked:
                if cycle % 20 == 1:  # 每分钟提示一次
                    print(f"[{time_str}] 🔒 屏幕已锁定，继续监控...")
                # 屏幕锁定时，跳过窗口状态检查，直接进行检测
                window = get_target_window()
                if window:
                    should_trigger, trigger_reasons, details = comprehensive_detection(window['hwnd'])
                    if should_trigger:
                        if current_time - last_alert_time > ALERT_INTERVAL:
                            print(f"🚨 锁屏期间触发通知: {', '.join(trigger_reasons)}")
                            if send_dingding_msg(trigger_reasons):
                                last_alert_time = current_time
                                print(f"✅ 锁屏通知已发送")
                time.sleep(CHECK_INTERVAL)
                continue

            # 获取窗口状态
            window = get_target_window()
            if not window:
                print(f"[{time_str}] ❌ 未找到'消息'窗口")
                time.sleep(CHECK_INTERVAL)
                continue

            # 检查窗口状态 - 改进逻辑
            window_state = window['window_state']
            is_minimized = window['is_minimized']

            # 只在窗口最小化或后台时进行检测（不在前台显示时）
            if window['is_foreground']:
                if cycle % 20 == 1:  # 每分钟提示一次
                    print(f"[{time_str}] 👁️  窗口在前台显示中，暂停监控")
                time.sleep(CHECK_INTERVAL)
                continue

            # 窗口在后台或最小化，进行检测
            if cycle % 20 == 1:  # 每分钟提示一次状态
                print(f"[{time_str}] 🔍 窗口状态: {window_state}，开始检测...")

            # 多条件综合检测
            should_trigger, trigger_reasons, details = comprehensive_detection(window['hwnd'])

            # 显示检测结果
            if should_trigger or cycle % 20 == 1:
                status = "触发" if should_trigger else "正常"
                print(f"\n[{time_str}] 🔍 检测结果: {status}")
                if trigger_reasons:
                    print(f"    触发原因: {', '.join(trigger_reasons)}")
                print(f"    {details}")

            # 触发通知
            if should_trigger:
                if current_time - last_alert_time > ALERT_INTERVAL:
                    print(f"🚨 触发通知: {', '.join(trigger_reasons)}")
                    if send_dingding_msg(trigger_reasons):
                        last_alert_time = current_time
                        print(f"✅ 通知已发送")
                else:
                    remaining = ALERT_INTERVAL - (current_time - last_alert_time)
                    print(f"⏰ 检测到触发条件但防抖中，还需等待 {remaining/60:.1f} 分钟")

            time.sleep(CHECK_INTERVAL)

    except KeyboardInterrupt:
        print("\n✅ 多条件监控已停止")
        sound_monitoring = False
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        import traceback
        traceback.print_exc()
        sound_monitoring = False
