"""
工具函数模块
包含自然排序、前缀处理、路径显示等通用功能
"""
import re
from pathlib import Path


def natural_sort_key(text):
    """
    自然排序键函数，正确处理数字排序
    例如：1, 2, 10, 11 而不是 1, 10, 11, 2
    """
    def convert(text_part):
        return int(text_part) if text_part.isdigit() else text_part.lower()
    
    return [convert(c) for c in re.split('([0-9]+)', text)]


def remove_number_prefix(name):
    """
    去除数字前缀
    支持格式：1. 、1- 、01_ 、001_ 等
    """
    # 支持多种数字前缀格式：数字+点、数字+连字符、数字+下划线
    pattern = r'^(\d+[\.\-_]\s*)'
    return re.sub(pattern, '', name).strip()


def get_display_name(file_path, max_length=50):
    """
    获取用于显示的文件路径名称
    如果路径过长，只显示到上一级目录
    
    Args:
        file_path (str): 完整文件路径
        max_length (int): 最大显示长度
    
    Returns:
        str: 适合显示的路径名称
    """
    path_obj = Path(file_path)
    
    # 如果路径长度在限制内，直接返回
    if len(str(path_obj)) <= max_length:
        return str(path_obj)
    
    # 如果路径过长，只显示父目录名和文件名
    parent_name = path_obj.parent.name
    file_name = path_obj.name
    
    # 构造简化显示：父目录/文件名
    display_name = f"{parent_name}/{file_name}"
    
    # 如果还是太长，进一步截断父目录名
    if len(display_name) > max_length:
        max_parent_length = max_length - len(file_name) - 4  # 留出空间给 ".../" 和文件名
        if max_parent_length > 0:
            truncated_parent = parent_name[:max_parent_length] + "..."
            display_name = f"{truncated_parent}/{file_name}"
        else:
            # 如果文件名本身就很长，只显示文件名
            display_name = file_name
    
    return display_name
