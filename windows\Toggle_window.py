import time
import ctypes
from pynput import mouse
import pygetwindow as gw

# 配置
TARGET_WINDOW_KEYWORD = "模拟器"  # 替换为你的窗口标题关键字
DOUBLE_CLICK_INTERVAL = 0.3      # 双击最大间隔时间
last_click_time = 0
click_count = 0

# Windows API
GWL_EXSTYLE = -20
WS_EX_LAYERED = 0x80000
LWA_ALPHA = 0x2

user32 = ctypes.windll.user32
SetWindowLong = user32.SetWindowLongW
GetWindowLong = user32.GetWindowLongW
SetLayeredWindowAttributes = user32.SetLayeredWindowAttributes

# 设置窗口透明度（0-255）
def set_window_alpha(hwnd, alpha):
    ex_style = GetWindowLong(hwnd, GWL_EXSTYLE)
    SetWindowLong(hwnd, GWL_EXSTYLE, ex_style | WS_EX_LAYERED)
    SetLayeredWindowAttributes(hwnd, 0, alpha, LWA_ALPHA)

# 获取窗口句柄
def get_target_hwnd():
    windows = gw.getWindowsWithTitle(TARGET_WINDOW_KEYWORD)
    return windows[0]._hWnd if windows else None

# 鼠标监听
def on_click(x, y, button, pressed):
    global last_click_time, click_count

    if button == mouse.Button.middle and pressed:
        hwnd = get_target_hwnd()
        if not hwnd:
            print(f"未找到包含【{TARGET_WINDOW_KEYWORD}】的窗口")
            return

        now = time.time()
        if now - last_click_time <= DOUBLE_CLICK_INTERVAL:
            click_count += 1
        else:
            click_count = 1
        last_click_time = now

        if click_count == 1:
            print("单击 → 设置透明度 0")
            set_window_alpha(hwnd, 0)
        elif click_count == 2:
            print("双击 → 恢复透明度 255")
            set_window_alpha(hwnd, 255)
            click_count = 0

# 启动监听
def start_listener():
    print("中键单击 = 隐形，双击 = 还原，Ctrl+C 退出")
    with mouse.Listener(on_click=on_click) as listener:
        listener.join()

if __name__ == "__main__":
    start_listener()
