import requests

DINGDING_WEBHOOK = "https://oapi.dingtalk.com/robot/send?access_token=ee5b869e1a3c55cc799af160c17a9ec3f76e9073eb98ab83c35ac5ddf128e527"

def test_keywords():
    """测试不同的关键词"""
    test_keywords = [
        "来消息了",
        "XGJ", 
        "闲管家",
        "消息",
        "通知",
        "提醒",
        "警报",
        "检测",
        "监控"
    ]
    
    print("🧪 开始测试钉钉机器人关键词...")
    print("=" * 50)
    
    for keyword in test_keywords:
        msg = f"{keyword}：这是一条测试消息"
        print(f"\n测试关键词: '{keyword}'")
        print(f"消息内容: {msg}")
        
        try:
            response = requests.post(DINGDING_WEBHOOK, json={
                "msgtype": "text",
                "text": {"content": msg}
            }, timeout=5)
            
            print(f"状态码: {response.status_code}")
            result = response.json()
            
            if result.get("errcode") == 0:
                print("✅ 成功！这个关键词有效")
                return keyword
            else:
                print(f"❌ 失败: {result.get('errmsg', '未知错误')}")
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
    
    print("\n" + "=" * 50)
    print("❌ 所有测试关键词都失败了")
    print("💡 建议:")
    print("1. 联系钉钉群管理员查看机器人设置的关键词")
    print("2. 或者在钉钉群中发送 '@机器人名称' 查看帮助信息")
    return None

if __name__ == "__main__":
    valid_keyword = test_keywords()
    if valid_keyword:
        print(f"\n🎉 找到有效关键词: '{valid_keyword}'")
        print(f"请在 send_dingding_msg.py 中设置: SECURITY_KEYWORD = '{valid_keyword}'")
