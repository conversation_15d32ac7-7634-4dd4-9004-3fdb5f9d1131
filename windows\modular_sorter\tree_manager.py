"""
树形管理器
处理文件夹树形结构和文件移动功能
包含修复的路径显示和文件重新选中功能
"""
import tkinter as tk
from tkinter import ttk, messagebox
import os
import shutil
from pathlib import Path
from tkinterdnd2 import DND_FILES
from utils import natural_sort_key, get_display_name


class TreeManager:
    """树形管理器"""
    
    def __init__(self, parent_frame, status_callback=None):
        """
        初始化树形管理器
        
        Args:
            parent_frame: 父框架
            status_callback: 状态更新回调函数
        """
        self.parent_frame = parent_frame
        self.status_callback = status_callback
        
        # 数据存储
        self.tree_folders = []
        
        # UI组件
        self.folder_tree = None
        self.files_listbox = None
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.parent_frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 左侧：文件夹树形结构
        left_frame = ttk.LabelFrame(main_frame, text="文件夹结构")
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

        # 树形控件
        tree_frame = ttk.Frame(left_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.folder_tree = ttk.Treeview(tree_frame, selectmode='extended')
        self.folder_tree.heading('#0', text='文件夹结构', anchor='w')

        # 滚动条
        tree_scrollbar_v = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.folder_tree.yview)
        tree_scrollbar_h = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.folder_tree.xview)
        self.folder_tree.configure(yscrollcommand=tree_scrollbar_v.set, xscrollcommand=tree_scrollbar_h.set)

        # 布局
        self.folder_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        tree_scrollbar_v.pack(side=tk.RIGHT, fill=tk.Y)
        tree_scrollbar_h.pack(side=tk.BOTTOM, fill=tk.X)

        # 右侧：待移动文件列表
        right_frame = ttk.LabelFrame(main_frame, text="待移动文件")
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))

        # 文件列表
        files_frame = ttk.Frame(right_frame)
        files_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.files_listbox = tk.Listbox(files_frame, selectmode=tk.EXTENDED)

        # 滚动条
        files_scrollbar = ttk.Scrollbar(files_frame, orient=tk.VERTICAL, command=self.files_listbox.yview)
        self.files_listbox.configure(yscrollcommand=files_scrollbar.set)

        # 布局
        self.files_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        files_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 底部按钮
        button_frame = ttk.Frame(self.parent_frame)
        button_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        ttk.Button(button_frame, text="清空文件夹", command=self.tree_clear_folders).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="清空文件", command=self.tree_clear_files).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="移动选中文件", command=self.tree_move_files).pack(side=tk.RIGHT)

        # 绑定事件
        self.folder_tree.bind('<Double-1>', self.on_tree_double_click)
        self.files_listbox.bind('<Double-1>', self.on_file_double_click)

        # 设置拖拽支持
        self.folder_tree.drop_target_register(DND_FILES)
        self.folder_tree.dnd_bind('<<Drop>>', self.on_tree_folder_drop)
        self.files_listbox.drop_target_register(DND_FILES)
        self.files_listbox.dnd_bind('<<Drop>>', self.on_tree_file_drop)

        # 设置键盘事件
        self.folder_tree.bind('<F5>', self.on_f5_refresh)
        self.files_listbox.bind('<F5>', self.on_f5_refresh)
        self.folder_tree.focus_set()  # 确保可以接收键盘事件

    def on_tree_folder_drop(self, event):
        """处理拖拽到文件夹树的事件"""
        try:
            # 获取根窗口来访问tk.splitlist
            root = self.parent_frame.winfo_toplevel()
            files = root.tk.splitlist(event.data)
            folder_count = 0
            for file_path in files:
                path_obj = Path(file_path)
                if path_obj.exists() and path_obj.is_dir():
                    self.add_folder_to_tree(file_path)
                    folder_count += 1
            self._update_status(f"已添加 {folder_count} 个文件夹")
        except Exception as e:
            self._update_status(f"添加文件夹失败: {str(e)}", "lightcoral")

    def on_tree_file_drop(self, event):
        """处理拖拽到文件列表的事件 - 修复了路径显示和自动选中问题，添加了排序功能"""
        try:
            # 获取根窗口来访问tk.splitlist
            root = self.parent_frame.winfo_toplevel()
            files = root.tk.splitlist(event.data)

            # 收集新添加的文件信息
            new_files = []
            for file_path in files:
                path_obj = Path(file_path)
                if path_obj.exists():
                    new_files.append({
                        'path': file_path,
                        'name': path_obj.name,
                        'display_name': get_display_name(file_path, max_length=60)
                    })

            if not new_files:
                return

            # 按文件名排序（去除数字前缀）
            from utils import remove_number_prefix
            new_files.sort(key=lambda x: natural_sort_key(remove_number_prefix(x['name'])))

            # 记录开始添加的位置
            start_index = self.files_listbox.size()

            # 添加排序后的文件到列表
            for i, file_info in enumerate(new_files):
                self.files_listbox.insert(tk.END, file_info['display_name'])

                # 存储完整路径映射
                if not hasattr(self, '_file_path_mapping'):
                    self._file_path_mapping = {}
                self._file_path_mapping[start_index + i] = file_info['path']

            # 自动选中新添加的文件 - 修复重新选中问题
            added_count = len(new_files)
            if added_count > 0:
                self.files_listbox.selection_clear(0, tk.END)  # 清除之前的选择
                for i in range(start_index, start_index + added_count):
                    self.files_listbox.selection_set(i)  # 选中新添加的文件

                # 确保选中的文件可见
                if start_index < self.files_listbox.size():
                    self.files_listbox.see(start_index)

            self._update_status(f"已添加并选中 {added_count} 个文件/文件夹（已按名称排序）")
        except Exception as e:
            self._update_status(f"添加文件失败: {str(e)}", "lightcoral")

    def add_folder_to_tree(self, folder_path):
        """添加文件夹到树形结构"""
        path_obj = Path(folder_path)
        if not path_obj.exists() or not path_obj.is_dir():
            return

        # 检查是否已存在
        for item in self.tree_folders:
            if item == folder_path:
                return

        self.tree_folders.append(folder_path)

        # 重新构建整个树形结构以确保正确排序
        self.rebuild_folder_tree()

    def rebuild_folder_tree(self):
        """重新构建文件夹树形结构，确保正确排序"""
        # 清空当前树形结构
        self.folder_tree.delete(*self.folder_tree.get_children())

        # 对根级文件夹进行自然排序（去除数字前缀）
        from utils import remove_number_prefix
        sorted_folders = sorted(self.tree_folders, key=lambda x: natural_sort_key(remove_number_prefix(Path(x).name)))

        # 重新添加所有文件夹
        for folder_path in sorted_folders:
            path_obj = Path(folder_path)
            folder_name = path_obj.name
            parent_id = self.folder_tree.insert('', 'end', text=f"📁 {folder_name}",
                                               values=(folder_path,), open=True)

            # 递归添加子文件夹
            self.add_subfolders_to_tree(folder_path, parent_id)

    def add_subfolders_to_tree(self, parent_path, parent_id):
        """递归添加子文件夹"""
        try:
            parent_obj = Path(parent_path)
            # 获取所有子文件夹并按自然排序（去除数字前缀）
            subdirs = [item for item in parent_obj.iterdir() if item.is_dir()]
            from utils import remove_number_prefix
            subdirs.sort(key=lambda x: natural_sort_key(remove_number_prefix(x.name)))  # 使用自然排序

            for item in subdirs:
                folder_name = item.name
                child_id = self.folder_tree.insert(parent_id, 'end',
                                                  text=f"📁 {folder_name}",
                                                  values=(str(item),), open=False)
                # 递归添加子文件夹
                self.add_subfolders_to_tree(str(item), child_id)
        except PermissionError:
            # 跳过无权限访问的文件夹
            pass
        except Exception as e:
            print(f"添加子文件夹时出错: {e}")

    def on_tree_double_click(self, event):
        """双击文件夹树项目"""
        selection = self.folder_tree.selection()
        if selection:
            item = selection[0]
            values = self.folder_tree.item(item, 'values')
            if values:
                folder_path = values[0]
                # 在文件管理器中打开文件夹
                try:
                    os.startfile(folder_path)
                except Exception as e:
                    messagebox.showerror("错误", f"无法打开文件夹: {str(e)}")

    def on_file_double_click(self, event):
        """双击文件列表项目"""
        selection = self.files_listbox.curselection()
        if selection:
            index = selection[0]
            # 获取真实的文件路径
            if hasattr(self, '_file_path_mapping') and index in self._file_path_mapping:
                file_path = self._file_path_mapping[index]
            else:
                # 如果没有映射，使用显示的文本（可能不完整）
                file_path = self.files_listbox.get(index)
            
            # 在文件管理器中打开文件或文件夹
            try:
                os.startfile(file_path)
            except Exception as e:
                messagebox.showerror("错误", f"无法打开文件: {str(e)}")

    def tree_clear_folders(self):
        """清空文件夹树"""
        self.tree_folders.clear()
        self.folder_tree.delete(*self.folder_tree.get_children())
        self._update_status("已清空文件夹树", "lightblue")

    def tree_clear_files(self):
        """清空文件列表"""
        self.files_listbox.delete(0, tk.END)
        if hasattr(self, '_file_path_mapping'):
            self._file_path_mapping.clear()
        self._update_status("已清空文件列表", "lightblue")

    def tree_move_files(self):
        """移动选中的文件到选中的文件夹"""
        # 获取选中的文件夹
        tree_selection = self.folder_tree.selection()
        if not tree_selection:
            messagebox.showwarning("警告", "请先选择目标文件夹")
            return

        target_folder = self.folder_tree.item(tree_selection[0], 'values')[0]
        if not Path(target_folder).exists():
            messagebox.showerror("错误", "目标文件夹不存在")
            return

        # 获取选中的文件
        file_selection = self.files_listbox.curselection()
        if not file_selection:
            messagebox.showwarning("警告", "请先选择要移动的文件")
            return

        # 确认移动
        file_count = len(file_selection)
        if not messagebox.askyesno("确认", f"确定要移动 {file_count} 个文件/文件夹到\n{target_folder} 吗？"):
            return

        success_count = 0
        error_count = 0

        # 从后往前删除，避免索引变化
        for i in reversed(file_selection):
            # 获取真实的文件路径
            if hasattr(self, '_file_path_mapping') and i in self._file_path_mapping:
                source_path = self._file_path_mapping[i]
            else:
                # 如果没有映射，使用显示的文本（可能不完整）
                source_path = self.files_listbox.get(i)

            source_obj = Path(source_path)

            try:
                if source_obj.exists():
                    target_path = Path(target_folder) / source_obj.name

                    # 如果目标已存在，询问是否覆盖
                    if target_path.exists():
                        if not messagebox.askyesno("文件已存在",
                                                 f"目标位置已存在 {source_obj.name}\n是否覆盖？"):
                            continue
                        if target_path.is_dir():
                            shutil.rmtree(target_path)
                        else:
                            target_path.unlink()

                    # 移动文件/文件夹
                    shutil.move(str(source_obj), str(target_path))

                    # 从列表中删除
                    self.files_listbox.delete(i)

                    # 更新路径映射
                    if hasattr(self, '_file_path_mapping'):
                        if i in self._file_path_mapping:
                            del self._file_path_mapping[i]
                        # 重新索引映射（因为删除了一个项目）
                        new_mapping = {}
                        for old_index, path in self._file_path_mapping.items():
                            if old_index > i:
                                new_mapping[old_index - 1] = path
                            else:
                                new_mapping[old_index] = path
                        self._file_path_mapping = new_mapping

                    success_count += 1
                else:
                    error_count += 1

            except Exception as e:
                error_count += 1
                print(f"移动文件失败: {e}")

        # 刷新目标文件夹的显示
        if success_count > 0:
            self.refresh_folder_contents(target_folder)

        # 显示结果
        if error_count == 0:
            messagebox.showinfo("成功", f"成功移动 {success_count} 个文件/文件夹")
            self._update_status(f"成功移动 {success_count} 个文件")
        else:
            messagebox.showwarning("部分成功", f"成功移动 {success_count} 个文件/文件夹\n失败 {error_count} 个文件/文件夹")
            self._update_status(f"移动完成，{error_count} 个失败", "orange")

    def handle_system_drop(self, files):
        """处理系统拖拽的文件 - 清空之前的数据，加载新数据"""
        # 清空之前的数据
        self.tree_folders.clear()
        self.files_listbox.delete(0, tk.END)
        if hasattr(self, '_file_path_mapping'):
            self._file_path_mapping.clear()

        folder_count = 0
        new_files = []

        # 分离文件夹和文件
        for file_path in files:
            path_obj = Path(file_path)
            if path_obj.exists():
                if path_obj.is_dir():
                    self.add_folder_to_tree(file_path)
                    folder_count += 1
                else:
                    new_files.append({
                        'path': file_path,
                        'name': path_obj.name,
                        'display_name': get_display_name(file_path, max_length=60)
                    })

        # 对文件进行排序（去除数字前缀）
        if new_files:
            from utils import remove_number_prefix
            new_files.sort(key=lambda x: natural_sort_key(remove_number_prefix(x['name'])))

            # 记录开始添加文件的位置
            start_index = self.files_listbox.size()

            # 添加排序后的文件
            for i, file_info in enumerate(new_files):
                self.files_listbox.insert(tk.END, file_info['display_name'])

                # 存储完整路径映射
                if not hasattr(self, '_file_path_mapping'):
                    self._file_path_mapping = {}
                self._file_path_mapping[start_index + i] = file_info['path']

            # 自动选中新添加的文件
            file_count = len(new_files)
            self.files_listbox.selection_clear(0, tk.END)  # 清除之前的选择
            for i in range(start_index, start_index + file_count):
                self.files_listbox.selection_set(i)  # 选中新添加的文件

            # 确保选中的文件可见
            if start_index < self.files_listbox.size():
                self.files_listbox.see(start_index)

        file_count = len(new_files)
        self._update_status(f"已清空并重新加载 {folder_count} 个文件夹，{file_count} 个文件并自动选中（已按名称排序）")

    def refresh_folder_contents(self, folder_path):
        """刷新指定文件夹的内容显示"""
        try:
            # 重新构建整个树形结构以确保显示最新内容
            self.rebuild_folder_tree()
            self._update_status(f"已刷新文件夹: {Path(folder_path).name}", "lightblue")
        except Exception as e:
            self._update_status(f"刷新文件夹失败: {str(e)}", "lightcoral")

    def on_f5_refresh(self, event=None):
        """F5刷新列表中的文件夹和文件"""
        # 刷新文件夹树
        if self.tree_folders:
            # 检查文件夹是否仍然存在，移除不存在的文件夹
            valid_folders = []
            removed_folder_count = 0

            for folder_path in self.tree_folders:
                path_obj = Path(folder_path)
                if path_obj.exists() and path_obj.is_dir():
                    valid_folders.append(folder_path)
                else:
                    removed_folder_count += 1

            self.tree_folders = valid_folders

            # 重新构建文件夹树
            if self.tree_folders:
                self.rebuild_folder_tree()

        # 刷新文件列表
        if hasattr(self, '_file_path_mapping') and self._file_path_mapping:
            valid_files = []
            removed_file_count = 0

            # 检查文件是否仍然存在
            for index, file_path in self._file_path_mapping.items():
                path_obj = Path(file_path)
                if path_obj.exists():
                    valid_files.append({
                        'path': file_path,
                        'name': path_obj.name,
                        'display_name': get_display_name(file_path, max_length=60)
                    })
                else:
                    removed_file_count += 1

            # 重新构建文件列表
            self.files_listbox.delete(0, tk.END)
            self._file_path_mapping.clear()

            if valid_files:
                # 排序文件
                from utils import remove_number_prefix
                valid_files.sort(key=lambda x: natural_sort_key(remove_number_prefix(x['name'])))

                # 重新添加文件
                for i, file_info in enumerate(valid_files):
                    self.files_listbox.insert(tk.END, file_info['display_name'])
                    self._file_path_mapping[i] = file_info['path']

            total_removed = (removed_folder_count if 'removed_folder_count' in locals() else 0) + removed_file_count
            if total_removed > 0:
                self._update_status(f"已刷新列表，移除了 {total_removed} 个不存在的项目", "orange")
            else:
                self._update_status("已刷新列表，所有项目有效", "lightblue")
        else:
            if self.tree_folders:
                self._update_status("已刷新文件夹树", "lightblue")
            else:
                self._update_status("列表为空，无需刷新", "lightblue")

    def _update_status(self, message, color="lightgreen"):
        """更新状态信息"""
        if self.status_callback:
            self.status_callback(message, color)
