import time
import requests
import win32gui
import win32con
import win32api
import ctypes
from ctypes import wintypes
from datetime import datetime
import threading

# 配置
WINDOW_KEYWORD = "消息"
DINGDING_WEBHOOK = "https://oapi.dingtalk.com/robot/send?access_token=ee5b869e1a3c55cc799af160c17a9ec3f76e9073eb98ab83c35ac5ddf128e527"
SECURITY_KEYWORD = "来消息了"

CHECK_INTERVAL = 1  # 1秒检查一次
ALERT_INTERVAL = 60  # 1分钟防抖

# 全局变量
last_alert_time = 0
last_flash_state = False

def send_dingding_msg(reason):
    """发送钉钉消息"""
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    msg = f"{SECURITY_KEYWORD}：{current_time} 检测到{WINDOW_KEYWORD}任务栏图标高亮({reason})，有顾客消息请尽快查看！"
    print(f"🚀 发送钉钉消息: {msg}")
    try:
        response = requests.post(DINGDING_WEBHOOK, json={
            "msgtype": "text",
            "text": {"content": msg}
        }, timeout=5)
        
        if response.status_code == 200 and response.json().get("errcode") == 0:
            print("✅ 钉钉消息发送成功")
            return True
        else:
            print(f"❌ 钉钉消息发送失败: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 发送钉钉消息时出错: {e}")
        return False

def get_target_window():
    """获取目标窗口"""
    def enum_handler(hwnd, result):
        try:
            if win32gui.IsWindowVisible(hwnd):
                title = win32gui.GetWindowText(hwnd)
                if title == WINDOW_KEYWORD:
                    result.append({
                        'hwnd': hwnd,
                        'title': title,
                        'is_minimized': win32gui.IsIconic(hwnd),
                        'is_foreground': win32gui.GetForegroundWindow() == hwnd
                    })
        except:
            pass
    
    windows = []
    win32gui.EnumWindows(enum_handler, windows)
    return windows[0] if windows else None

def check_flash_state_direct(hwnd):
    """直接检查窗口闪烁状态"""
    try:
        user32 = ctypes.windll.user32
        
        # 定义FLASHWINFO结构
        class FLASHWINFO(ctypes.Structure):
            _fields_ = [
                ('cbSize', wintypes.UINT),
                ('hwnd', wintypes.HWND),
                ('dwFlags', wintypes.DWORD),
                ('uCount', wintypes.UINT),
                ('dwTimeout', wintypes.DWORD)
            ]
        
        # 检查当前闪烁状态
        flash_info = FLASHWINFO()
        flash_info.cbSize = ctypes.sizeof(FLASHWINFO)
        flash_info.hwnd = hwnd
        flash_info.dwFlags = 0  # FLASHW_STOP
        flash_info.uCount = 0
        flash_info.dwTimeout = 0
        
        # 调用FlashWindowEx，如果返回非零值说明之前在闪烁
        result = user32.FlashWindowEx(ctypes.byref(flash_info))
        
        return bool(result), f"FlashWindowEx返回: {result}"
        
    except Exception as e:
        return False, f"闪烁检测异常: {e}"

def check_window_attention_state(hwnd):
    """检查窗口是否需要用户注意"""
    try:
        # 方法1: 检查窗口状态
        is_minimized = win32gui.IsIconic(hwnd)
        is_visible = win32gui.IsWindowVisible(hwnd)
        is_foreground = win32gui.GetForegroundWindow() == hwnd
        
        # 方法2: 检查窗口的Flash状态
        flash_result, flash_reason = check_flash_state_direct(hwnd)
        
        # 方法3: 检查窗口类名和属性
        try:
            class_name = win32gui.GetClassName(hwnd)
            window_info = f"类名: {class_name}"
        except:
            window_info = "无法获取类名"
        
        # 方法4: 检查窗口的显示状态
        try:
            placement = win32gui.GetWindowPlacement(hwnd)
            show_state = placement[1]  # SW_HIDE=0, SW_NORMAL=1, SW_MINIMIZED=2, SW_MAXIMIZED=3
            show_state_name = {0: "隐藏", 1: "正常", 2: "最小化", 3: "最大化"}.get(show_state, f"未知({show_state})")
        except:
            show_state_name = "无法获取"
        
        # 综合判断
        attention_needed = False
        reasons = []
        
        if flash_result:
            attention_needed = True
            reasons.append("窗口闪烁")
        
        if is_minimized and is_visible and not is_foreground:
            attention_needed = True
            reasons.append("最小化但可见")
        
        # 检查窗口是否有未读消息的其他迹象
        try:
            user32 = ctypes.windll.user32
            # 检查窗口消息队列
            msg_count = user32.GetQueueStatus(0x1FF)
            if msg_count > 0:
                attention_needed = True
                reasons.append(f"有{msg_count}个未处理消息")
        except:
            pass
        
        detail = f"状态: {show_state_name}, 可见: {is_visible}, 前台: {is_foreground}, {window_info}, {flash_reason}"
        
        return attention_needed, reasons, detail
        
    except Exception as e:
        return False, [], f"检测异常: {e}"

if __name__ == "__main__":
    print("🎯 任务栏高亮专项检测 - 闲管家顾客消息监控")
    print("=" * 70)
    print(f"检查间隔: {CHECK_INTERVAL}秒")
    print(f"防抖间隔: {ALERT_INTERVAL//60}分钟")
    print("=" * 70)
    print("🔍 专项检测策略:")
    print("  1. 直接检查FlashWindowEx API状态")
    print("  2. 检查窗口显示状态组合")
    print("  3. 检查窗口消息队列")
    print("  4. 多重验证确保准确性")
    print("按 Ctrl+C 退出...")
    print("=" * 70)
    
    # 检查初始状态
    window = get_target_window()
    if window:
        status = "最小化" if window['is_minimized'] else "显示中"
        print(f"✅ 找到目标窗口: {window['title']} ({status})")
        
        # 初始检测
        attention_needed, reasons, detail = check_window_attention_state(window['hwnd'])
        print(f"🔍 初始状态: {'需要注意' if attention_needed else '正常'}")
        if reasons:
            print(f"    原因: {', '.join(reasons)}")
        print(f"    详情: {detail}")
    else:
        print("❌ 未找到'消息'窗口，请确保闲管家正在运行")
    
    print()
    print("🔍 开始专项监控...")
    
    try:
        cycle = 0
        while True:
            cycle += 1
            current_time = time.time()
            time_str = datetime.now().strftime("%H:%M:%S")
            
            # 获取窗口状态
            window = get_target_window()
            if not window:
                if cycle % 60 == 1:
                    print(f"[{time_str}] ❌ 未找到'消息'窗口")
                time.sleep(CHECK_INTERVAL)
                continue
            
            # 只在窗口非前台时检测
            if window['is_foreground']:
                if cycle % 60 == 1:
                    print(f"[{time_str}] 👁️  窗口在前台，暂停监控")
                time.sleep(CHECK_INTERVAL)
                continue
            
            # 专项检测
            attention_needed, reasons, detail = check_window_attention_state(window['hwnd'])
            
            # 显示检测结果
            if attention_needed or cycle % 60 == 1:
                status = "🚨需要注意" if attention_needed else "正常"
                print(f"[{time_str}] {status}")
                if reasons:
                    print(f"    原因: {', '.join(reasons)}")
                print(f"    详情: {detail}")
            
            # 触发通知
            if attention_needed:
                if current_time - last_alert_time > ALERT_INTERVAL:
                    reason_text = ', '.join(reasons)
                    print(f"🚨 立即通知: {reason_text}")
                    if send_dingding_msg(reason_text):
                        last_alert_time = current_time
                        print(f"✅ 通知已发送")
                else:
                    remaining = ALERT_INTERVAL - (current_time - last_alert_time)
                    print(f"⏰ 检测到高亮但防抖中，{remaining:.0f}秒后可再次通知")
            
            time.sleep(CHECK_INTERVAL)
            
    except KeyboardInterrupt:
        print("\n✅ 专项监控已停止")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        import traceback
        traceback.print_exc()
