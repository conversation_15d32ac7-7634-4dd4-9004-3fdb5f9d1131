#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动合并移动管理器
支持将多个文件/文件夹合并移动到第一个项目的同级目录下的新文件夹中
"""

import tkinter as tk
from tkinter import ttk, messagebox
from pathlib import Path
import shutil
import re
from utils import natural_sort_key, remove_number_prefix, get_display_name


class AutoMergeMover:
    """自动合并移动管理器"""
    
    def __init__(self, parent_frame, status_callback=None):
        """初始化自动合并移动管理器"""
        self.parent_frame = parent_frame
        self.status_callback = status_callback
        self.items = []  # 存储文件/文件夹信息
        
    def setup_auto_merge_tab(self, notebook):
        """设置自动合并移动标签页"""
        # 创建标签页
        self.tab_frame = ttk.Frame(notebook)
        notebook.add(self.tab_frame, text="自动合并移动")
        
        # 创建主要布局
        main_frame = ttk.Frame(self.tab_frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 说明标签
        info_frame = ttk.LabelFrame(main_frame, text="功能说明", padding=10)
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        info_text = """拖入多个文件/文件夹，点击"合并移动"按钮：
• 在第一个项目的同级目录下创建新文件夹
• 将所有选中的项目移动到新文件夹中
• 新文件夹名称：多个项目用"+"连接，单个项目保持原名（去除序号和后缀）"""
        
        info_label = tk.Label(info_frame, text=info_text, justify=tk.LEFT, 
                             font=("Arial", 9), wraplength=600)
        info_label.pack(anchor=tk.W)
        
        # 文件列表区域
        list_frame = ttk.LabelFrame(main_frame, text="待处理文件/文件夹列表", padding=10)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 配置grid权重
        list_frame.grid_rowconfigure(0, weight=1)
        list_frame.grid_columnconfigure(0, weight=1)
        
        # 创建Treeview来显示文件信息
        columns = ('name', 'type', 'path')
        self.tree = ttk.Treeview(list_frame, columns=columns, show='tree headings', selectmode='extended')
        
        # 设置列标题
        self.tree.heading('#0', text='序号')
        self.tree.heading('name', text='名称')
        self.tree.heading('type', text='类型')
        self.tree.heading('path', text='路径')
        
        # 设置列宽
        self.tree.column('#0', width=50, minwidth=50)
        self.tree.column('name', width=200, minwidth=150)
        self.tree.column('type', width=80, minwidth=60)
        self.tree.column('path', width=300, minwidth=200)
        
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 滚动条
        tree_scrollbar_v = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.tree.yview)
        tree_scrollbar_h = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=tree_scrollbar_v.set, xscrollcommand=tree_scrollbar_h.set)
        tree_scrollbar_v.grid(row=0, column=1, sticky=(tk.N, tk.S))
        tree_scrollbar_h.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        # 设置拖拽支持
        self.tree.drop_target_register('DND_Files')
        self.tree.dnd_bind('<<Drop>>', self.on_drop)
        
        # 设置键盘事件
        self.tree.bind('<F5>', self.on_f5_refresh)
        self.tree.focus_set()
        
        # 操作按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        # 左侧按钮
        left_buttons = ttk.Frame(button_frame)
        left_buttons.pack(side=tk.LEFT)
        
        ttk.Button(left_buttons, text="清空列表", command=self.clear_list).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(left_buttons, text="移除选中", command=self.remove_selected).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(left_buttons, text="预览结果", command=self.preview_merge).pack(side=tk.LEFT)
        
        # 右侧按钮
        right_buttons = ttk.Frame(button_frame)
        right_buttons.pack(side=tk.RIGHT)
        
        ttk.Button(right_buttons, text="合并移动", command=self.execute_merge_move, 
                  style="Accent.TButton").pack(side=tk.RIGHT)
        
    def on_drop(self, event):
        """处理拖拽事件 - 清空之前的数据，加载新数据"""
        try:
            # 清空之前的数据
            self.clear_list()
            
            # 获取根窗口来访问tk.splitlist
            root = self.parent_frame.winfo_toplevel()
            files = root.tk.splitlist(event.data)
            
            added_count = 0
            for file_path in files:
                path_obj = Path(file_path)
                if path_obj.exists():
                    self.items.append({
                        'path': file_path,
                        'name': path_obj.name,
                        'type': '文件夹' if path_obj.is_dir() else '文件',
                        'is_dir': path_obj.is_dir()
                    })
                    added_count += 1
            
            # 排序并刷新显示
            if self.items:
                self.sort_and_refresh_items()
                
            self._update_status(f"已清空并重新加载 {added_count} 个项目")
            
        except Exception as e:
            self._update_status(f"拖拽失败: {str(e)}", "lightcoral")
            
    def sort_and_refresh_items(self):
        """排序并刷新项目显示"""
        # 按名称排序（去除数字前缀）
        self.items.sort(key=lambda x: natural_sort_key(remove_number_prefix(x['name'])))
        
        # 刷新显示
        self.refresh_tree_display()
        
    def refresh_tree_display(self):
        """刷新树形显示"""
        # 清空树形控件
        for item in self.tree.get_children():
            self.tree.delete(item)
            
        # 添加项目
        for i, item in enumerate(self.items, 1):
            display_path = get_display_name(item['path'], max_length=50)
            self.tree.insert('', 'end', text=str(i), 
                           values=(item['name'], item['type'], display_path))
                           
    def clear_list(self):
        """清空列表"""
        self.items.clear()
        self.refresh_tree_display()
        self._update_status("已清空列表", "lightblue")
        
    def remove_selected(self):
        """移除选中的项目"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要移除的项目")
            return
            
        # 获取选中项目的索引
        selected_indices = []
        for item_id in selection:
            item_index = self.tree.index(item_id)
            selected_indices.append(item_index)
            
        # 从后往前删除，避免索引变化
        for i in sorted(selected_indices, reverse=True):
            if 0 <= i < len(self.items):
                self.items.pop(i)
                
        # 刷新显示
        self.refresh_tree_display()
        self._update_status(f"已移除 {len(selected_indices)} 个项目")
        
    def generate_merged_folder_name(self):
        """生成合并后的文件夹名称"""
        if not self.items:
            return ""

        if len(self.items) == 1:
            # 单个项目，去除序号和后缀
            name = self.items[0]['name']
            # 如果是文件，先去除后缀
            if not self.items[0]['is_dir']:
                name = Path(name).stem
            # 去除数字前缀
            name = remove_number_prefix(name)
            return name
        else:
            # 多个项目，用"+"连接
            names = []
            for item in self.items:
                name = item['name']
                # 如果是文件，先去除后缀
                if not item['is_dir']:
                    name = Path(name).stem
                # 去除数字前缀
                name = remove_number_prefix(name)
                names.append(name)
            return "+".join(names)
            
    def get_target_directory(self):
        """获取目标目录（第一个项目的同级目录）"""
        if not self.items:
            return None
        return Path(self.items[0]['path']).parent
        
    def preview_merge(self):
        """预览合并结果"""
        if not self.items:
            messagebox.showwarning("警告", "请先拖入文件或文件夹")
            return
            
        target_dir = self.get_target_directory()
        merged_name = self.generate_merged_folder_name()
        target_path = target_dir / merged_name
        
        # 构建预览信息
        preview_text = f"合并移动预览：\n\n"
        preview_text += f"目标目录：{target_dir}\n"
        preview_text += f"新文件夹名称：{merged_name}\n"
        preview_text += f"完整路径：{target_path}\n\n"
        preview_text += f"将要移动的项目（{len(self.items)}个）：\n"
        
        for i, item in enumerate(self.items, 1):
            preview_text += f"{i}. {item['name']} ({item['type']})\n"
            
        # 显示预览对话框
        preview_window = tk.Toplevel(self.parent_frame)
        preview_window.title("预览合并移动结果")
        preview_window.geometry("600x400")
        preview_window.transient(self.parent_frame.winfo_toplevel())
        preview_window.grab_set()
        
        text_widget = tk.Text(preview_window, wrap=tk.WORD, font=("Consolas", 10))
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        text_widget.insert(tk.END, preview_text)
        text_widget.config(state=tk.DISABLED)
        
        ttk.Button(preview_window, text="关闭", command=preview_window.destroy).pack(pady=10)
        
    def execute_merge_move(self):
        """执行合并移动"""
        if not self.items:
            messagebox.showwarning("警告", "请先拖入文件或文件夹")
            return
            
        target_dir = self.get_target_directory()
        merged_name = self.generate_merged_folder_name()
        target_path = target_dir / merged_name
        
        # 确认操作
        if not messagebox.askyesno("确认", 
                                 f"确定要将 {len(self.items)} 个项目合并移动到：\n{target_path}\n\n这个操作不可撤销！"):
            return
            
        try:
            # 创建目标文件夹
            target_path.mkdir(exist_ok=True)
            
            success_count = 0
            error_count = 0
            error_messages = []
            
            # 移动每个项目
            for item in self.items:
                try:
                    source_path = Path(item['path'])
                    dest_path = target_path / source_path.name
                    
                    # 如果目标已存在，添加序号
                    counter = 1
                    original_dest = dest_path
                    while dest_path.exists():
                        if source_path.is_dir():
                            dest_path = target_path / f"{original_dest.stem}_{counter}"
                        else:
                            dest_path = target_path / f"{original_dest.stem}_{counter}{original_dest.suffix}"
                        counter += 1
                    
                    # 执行移动
                    shutil.move(str(source_path), str(dest_path))
                    success_count += 1
                    
                except Exception as e:
                    error_count += 1
                    error_messages.append(f"{item['name']}: {str(e)}")
                    
            # 显示结果
            if error_count == 0:
                messagebox.showinfo("成功", f"成功合并移动 {success_count} 个项目到：\n{target_path}")
                self._update_status(f"成功合并移动 {success_count} 个项目")
                # 清空列表
                self.clear_list()
            else:
                error_text = "\n".join(error_messages[:3])  # 只显示前3个错误
                if len(error_messages) > 3:
                    error_text += f"\n... 还有 {len(error_messages) - 3} 个错误"
                messagebox.showwarning("部分成功", 
                                     f"成功移动 {success_count} 个项目\n失败 {error_count} 个\n\n错误详情：\n{error_text}")
                self._update_status(f"合并移动完成，{error_count} 个失败", "orange")
                
        except Exception as e:
            messagebox.showerror("错误", f"创建目标文件夹失败：{str(e)}")
            self._update_status(f"操作失败: {str(e)}", "lightcoral")
            
    def on_f5_refresh(self, event=None):
        """F5刷新列表中的文件/文件夹"""
        if not self.items:
            self._update_status("列表为空，无需刷新", "lightblue")
            return
            
        # 检查项目是否仍然存在，移除不存在的项目
        valid_items = []
        removed_count = 0
        
        for item in self.items:
            path_obj = Path(item['path'])
            if path_obj.exists():
                # 更新显示名称（可能已改变）
                item['name'] = path_obj.name
                item['type'] = '文件夹' if path_obj.is_dir() else '文件'
                item['is_dir'] = path_obj.is_dir()
                valid_items.append(item)
            else:
                removed_count += 1
                
        self.items = valid_items
        
        # 重新排序和显示
        if self.items:
            self.sort_and_refresh_items()
            
        if removed_count > 0:
            self._update_status(f"已刷新列表，移除了 {removed_count} 个不存在的项目", "orange")
        else:
            self._update_status("已刷新列表，所有项目有效", "lightblue")
            
    def handle_system_drop(self, files):
        """处理系统拖拽的文件 - 清空之前的数据，加载新数据"""
        # 清空之前的数据
        self.clear_list()
        
        added_count = 0
        for file_path in files:
            path_obj = Path(file_path)
            if path_obj.exists():
                self.items.append({
                    'path': file_path,
                    'name': path_obj.name,
                    'type': '文件夹' if path_obj.is_dir() else '文件',
                    'is_dir': path_obj.is_dir()
                })
                added_count += 1
        
        # 排序并刷新显示
        if self.items:
            self.sort_and_refresh_items()
            
        self._update_status(f"已清空并重新加载 {added_count} 个项目")
        
    def _update_status(self, message, color="lightgreen"):
        """更新状态栏"""
        if self.status_callback:
            self.status_callback(message, color)
