import time
import requests
import win32gui
import win32con
import win32api
import sounddevice as sd
import numpy as np
import threading
from collections import deque
import ctypes
from ctypes import wintypes

# 配置
WINDOW_KEYWORD = "消息"  # 根据识别结果确定的窗口关键词
DINGDING_WEBHOOK = "https://oapi.dingtalk.com/robot/send?access_token=ee5b869e1a3c55cc799af160c17a9ec3f76e9073eb98ab83c35ac5ddf128e527"
SECURITY_KEYWORD = "来消息了"

CHECK_INTERVAL = 5
ALERT_INTERVAL = 60

# 全局变量
last_alert_time = 0
sound_events = deque(maxlen=50)
sound_monitoring = True
last_window_state = {}
taskbar_flash_events = deque(maxlen=30)
flash_monitoring = True

# Windows API 常量
FLASHW_STOP = 0
FLASHW_CAPTION = 1
FLASHW_TRAY = 2
FLASHW_ALL = 3
FLASHW_TIMER = 4
FLASHW_TIMERNOFG = 12

def send_dingding_msg():
    """发送钉钉消息"""
    msg = f"{SECURITY_KEYWORD}：检测到{WINDOW_KEYWORD}有新消息，请及时查看电脑。"
    print(f"🚀 准备发送钉钉消息: {msg}")
    try:
        response = requests.post(DINGDING_WEBHOOK, json={
            "msgtype": "text",
            "text": {"content": msg}
        }, timeout=5)
        
        if response.status_code == 200 and response.json().get("errcode") == 0:
            print("✅ 钉钉消息发送成功")
        else:
            print(f"❌ 钉钉消息发送失败: {response.text}")
    except Exception as e:
        print(f"❌ 发送钉钉消息时出错: {e}")

def get_target_window():
    """获取目标窗口信息"""
    def enum_handler(hwnd, result):
        if win32gui.IsWindowVisible(hwnd):
            title = win32gui.GetWindowText(hwnd)
            if WINDOW_KEYWORD in title:
                result.append({
                    'hwnd': hwnd,
                    'title': title,
                    'is_minimized': win32gui.IsIconic(hwnd),
                    'is_foreground': win32gui.GetForegroundWindow() == hwnd
                })

    windows = []
    win32gui.EnumWindows(enum_handler, windows)
    return windows[0] if windows else None

def check_taskbar_flash_state(hwnd):
    """检查任务栏图标的闪烁状态"""
    try:
        # 方法1: 检查窗口是否正在闪烁
        # 使用GetWindowLong检查窗口的闪烁状态
        window_long = win32gui.GetWindowLong(hwnd, win32con.GWL_EXSTYLE)

        # 方法2: 检查窗口是否最小化但有活动
        is_minimized = win32gui.IsIconic(hwnd)
        is_visible = win32gui.IsWindowVisible(hwnd)
        is_foreground = win32gui.GetForegroundWindow() == hwnd

        # 方法3: 尝试检测任务栏按钮状态
        # 当窗口最小化且不是前台窗口时，检查是否有未读状态
        if is_minimized and is_visible and not is_foreground:
            # 这里我们假设如果窗口最小化但仍然可见，且有声音事件，
            # 那么任务栏图标可能正在闪烁或高亮
            return True

        return False

    except Exception as e:
        print(f"检查任务栏闪烁状态时出错: {e}")
        return False

def monitor_taskbar_flashing():
    """监控任务栏闪烁的后台线程"""
    global taskbar_flash_events, flash_monitoring

    print("⚡ 开始监控任务栏闪烁...")

    while flash_monitoring:
        try:
            window = get_target_window()
            if window:
                hwnd = window['hwnd']

                # 检查任务栏闪烁状态
                if check_taskbar_flash_state(hwnd):
                    current_time = time.time()

                    # 避免重复记录（2秒内的重复事件）
                    recent_events = [e for e in taskbar_flash_events if current_time - e['time'] < 2]
                    if not recent_events:
                        flash_event = {
                            'time': current_time,
                            'hwnd': hwnd,
                            'title': window['title']
                        }
                        taskbar_flash_events.append(flash_event)
                        print(f"⚡ 检测到任务栏可能闪烁: {window['title']}")

        except Exception as e:
            print(f"监控任务栏闪烁时出错: {e}")

        time.sleep(1)  # 每秒检查一次

def has_recent_taskbar_flash(seconds=15):
    """检查最近是否有任务栏闪烁事件"""
    current_time = time.time()
    recent_flashes = [e for e in taskbar_flash_events if current_time - e['time'] < seconds]

    if recent_flashes:
        print(f"⚡ 最近{seconds}秒内检测到 {len(recent_flashes)} 个任务栏闪烁事件")
        return True
    return False

def continuous_sound_monitor():
    """持续监听声音"""
    global sound_events, sound_monitoring
    
    def audio_callback(indata, frames, time_info, status):
        if sound_monitoring:
            volume = np.linalg.norm(indata) / len(indata)
            if volume > 0.02:  # 声音阈值
                import time as time_module
                sound_events.append({
                    'time': time_module.time(),
                    'volume': volume
                })
                print(f"🔊 检测到声音: 音量={volume:.4f}")
    
    try:
        print("🎧 开始声音监听...")
        with sd.InputStream(callback=audio_callback, channels=1, samplerate=44100, blocksize=1024):
            while sound_monitoring:
                time.sleep(0.1)
    except Exception as e:
        print(f"声音监听异常: {e}")

def has_recent_sound(seconds=10):
    """检查最近是否有声音"""
    current_time = time.time()
    recent_sounds = [e for e in sound_events if current_time - e['time'] < seconds]
    return len(recent_sounds) > 0

def detect_window_changes():
    """检测窗口状态变化"""
    global last_window_state
    
    window = get_target_window()
    if not window:
        return False
    
    hwnd = window['hwnd']
    current_state = {
        'title': window['title'],
        'is_minimized': window['is_minimized'],
        'is_foreground': window['is_foreground']
    }
    
    # 检查是否有状态变化
    if hwnd in last_window_state:
        prev_state = last_window_state[hwnd]
        
        # 检测有意义的变化
        changes = []
        
        # 标题变化（可能表示新消息）
        if prev_state['title'] != current_state['title']:
            changes.append(f"标题变化: '{prev_state['title']}' -> '{current_state['title']}'")
        
        # 从前台变为最小化（可能表示收到消息后最小化）
        if prev_state['is_foreground'] and current_state['is_minimized']:
            changes.append("窗口从前台变为最小化")
        
        # 从非最小化变为最小化
        if not prev_state['is_minimized'] and current_state['is_minimized']:
            changes.append("窗口被最小化")
        
        if changes:
            print(f"🔄 窗口状态变化: {window['title']}")
            for change in changes:
                print(f"   - {change}")
            last_window_state[hwnd] = current_state
            return True
    
    # 更新状态
    last_window_state[hwnd] = current_state
    return False

if __name__ == "__main__":
    print("🎯 简化版闲管家消息监控")
    print("=" * 50)
    print(f"监控窗口关键词: {WINDOW_KEYWORD}")
    print(f"检测间隔: {CHECK_INTERVAL}秒")
    print(f"防抖间隔: {ALERT_INTERVAL}秒")
    print("按 Ctrl+C 退出...")
    print()
    
    # 启动声音监听线程
    sound_thread = threading.Thread(target=continuous_sound_monitor, daemon=True)
    sound_thread.start()

    # 启动任务栏闪烁监听线程
    flash_thread = threading.Thread(target=monitor_taskbar_flashing, daemon=True)
    flash_thread.start()
    
    # 初始化窗口状态
    window = get_target_window()
    if window:
        print(f"✅ 找到目标窗口: {window['title']} (句柄: {window['hwnd']})")
        last_window_state[window['hwnd']] = {
            'title': window['title'],
            'is_minimized': window['is_minimized'],
            'is_foreground': window['is_foreground']
        }
    else:
        print(f"❌ 未找到包含'{WINDOW_KEYWORD}'的窗口")
    
    print("\n🔍 开始监控...")
    
    try:
        while True:
            triggered = False
            trigger_reasons = []

            # 1. 检查声音事件（最重要的指标）
            has_sound = has_recent_sound(10)
            if has_sound:
                trigger_reasons.append("检测到声音")

            # 2. 检查任务栏闪烁事件
            has_flash = has_recent_taskbar_flash(15)
            if has_flash:
                trigger_reasons.append("任务栏闪烁")

            # 3. 检查窗口状态变化
            if detect_window_changes():
                trigger_reasons.append("窗口状态变化")

            # 组合判断：声音 + 任务栏闪烁 = 很可能有新消息
            if has_sound and has_flash:
                triggered = True
                print("🎯 强信号：同时检测到声音和任务栏活动")
            elif has_sound:
                triggered = True
                print("🔊 检测到声音事件")
            elif has_flash:
                triggered = True
                print("⚡ 检测到任务栏闪烁")

            # 发送通知（带防抖）
            if triggered:
                current_time = time.time()
                if current_time - last_alert_time > ALERT_INTERVAL:
                    print(f"🚨 触发条件: {', '.join(trigger_reasons)}")
                    send_dingding_msg()
                    last_alert_time = current_time
                else:
                    remaining = ALERT_INTERVAL - (current_time - last_alert_time)
                    print(f"⏰ 防抖中，还需等待 {remaining:.1f} 秒")

            time.sleep(CHECK_INTERVAL)
            
    except KeyboardInterrupt:
        print("\n✅ 监控已停止")
        sound_monitoring = False
        flash_monitoring = False
