import time
import requests
import win32gui
import win32con

# 配置
WINDOW_KEYWORD = "消息"
DINGDING_WEBHOOK = "https://oapi.dingtalk.com/robot/send?access_token=ee5b869e1a3c55cc799af160c17a9ec3f76e9073eb98ab83c35ac5ddf128e527"
SECURITY_KEYWORD = "来消息了"

def send_dingding_msg():
    """发送钉钉消息"""
    msg = f"{SECURITY_KEYWORD}：检测到{WINDOW_KEYWORD}有新消息，请及时查看电脑。"
    print(f"🚀 准备发送钉钉消息: {msg}")
    try:
        response = requests.post(DINGDING_WEBHOOK, json={
            "msgtype": "text",
            "text": {"content": msg}
        }, timeout=5)
        
        if response.status_code == 200 and response.json().get("errcode") == 0:
            print("✅ 钉钉消息发送成功")
            return True
        else:
            print(f"❌ 钉钉消息发送失败: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 发送钉钉消息时出错: {e}")
        return False

def get_all_windows():
    """获取所有相关窗口的详细信息"""
    windows = []
    
    def enum_handler(hwnd, _):
        try:
            if win32gui.IsWindowVisible(hwnd):
                title = win32gui.GetWindowText(hwnd)
                if WINDOW_KEYWORD in title or "闲管家" in title:
                    is_minimized = win32gui.IsIconic(hwnd)
                    is_foreground = win32gui.GetForegroundWindow() == hwnd
                    class_name = win32gui.GetClassName(hwnd)
                    
                    windows.append({
                        'hwnd': hwnd,
                        'title': title,
                        'class_name': class_name,
                        'is_minimized': is_minimized,
                        'is_foreground': is_foreground,
                        'is_visible': win32gui.IsWindowVisible(hwnd)
                    })
        except Exception as e:
            print(f"处理窗口时出错: {e}")
    
    try:
        win32gui.EnumWindows(enum_handler, None)
    except Exception as e:
        print(f"枚举窗口时出错: {e}")
    
    return windows

def analyze_windows():
    """分析窗口状态"""
    print("🔍 正在搜索相关窗口...")
    windows = get_all_windows()
    
    if not windows:
        print("❌ 未找到任何相关窗口")
        return None
    
    print(f"\n📊 找到 {len(windows)} 个相关窗口:")
    print("-" * 80)
    
    target_window = None
    
    for i, window in enumerate(windows, 1):
        status_parts = []
        if window['is_minimized']:
            status_parts.append("最小化")
        if window['is_foreground']:
            status_parts.append("前台")
        if not window['is_minimized'] and not window['is_foreground']:
            status_parts.append("后台显示")
        
        status = f"[{', '.join(status_parts)}]" if status_parts else "[正常]"
        
        print(f"{i:2d}. 标题: {window['title']}")
        print(f"    状态: {status}")
        print(f"    句柄: {window['hwnd']}")
        print(f"    类名: {window['class_name']}")
        
        # 选择"消息"窗口作为主要监控目标
        if window['title'] == "消息":
            target_window = window
            print(f"    ⭐ 这是主要监控目标")
        
        print()
    
    return target_window

if __name__ == "__main__":
    print("🐛 简化调试版本 - 窗口状态检测")
    print("=" * 60)
    print("🎯 目标：确认能正确检测到窗口状态")
    print("=" * 60)
    
    try:
        cycle = 0
        last_alert_time = 0
        
        while True:
            cycle += 1
            print(f"\n{'='*15} 检测周期 {cycle} {'='*15}")
            
            # 分析窗口状态
            target_window = analyze_windows()
            
            if not target_window:
                print("❌ 未找到'消息'窗口，请确保闲管家正在运行")
            else:
                print(f"✅ 找到目标窗口: {target_window['title']}")
                
                if target_window['is_minimized']:
                    print("🎯 窗口已最小化 - 符合监控条件！")
                    
                    # 简单测试：每次检测到最小化状态就发送通知（用于测试）
                    current_time = time.time()
                    if current_time - last_alert_time > 60:  # 1分钟防抖
                        print("🚨 测试通知：检测到窗口最小化状态")
                        if send_dingding_msg():
                            last_alert_time = current_time
                            print("✅ 测试通知发送成功")
                    else:
                        remaining = 60 - (current_time - last_alert_time)
                        print(f"⏰ 防抖中，还需等待 {remaining:.1f} 秒")
                        
                else:
                    print("⏸️  窗口未最小化，暂停监控")
                    print("   请最小化闲管家窗口以开始监控")
            
            print(f"\n⏱️  等待 10 秒后进行下次检测...")
            time.sleep(10)
            
    except KeyboardInterrupt:
        print("\n✅ 调试监控已停止")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        import traceback
        traceback.print_exc()
