#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
树形管理模块
处理文件夹树形结构和文件移动功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import shutil
from pathlib import Path
from tkinterdnd2 import DND_FILES
from .utils import natural_sort_key, get_display_name


class TreeManager:
    """树形管理器"""
    
    def __init__(self, parent):
        self.parent = parent
        self.tree_folders = []  # 树形管理用
        
        # UI组件
        self.folder_tree = None
        self.files_listbox = None
        
    def setup_tree_manager_tab(self, notebook):
        """设置树形管理标签页"""
        # 创建标签页框架
        self.tree_frame = ttk.Frame(notebook)
        notebook.add(self.tree_frame, text="树形管理")

        # 创建主框架
        main_frame = ttk.Frame(self.tree_frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 左侧：文件夹树形结构
        left_frame = ttk.LabelFrame(main_frame, text="文件夹结构")
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

        # 树形控件
        tree_frame = ttk.Frame(left_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.folder_tree = ttk.Treeview(tree_frame, selectmode='extended')
        self.folder_tree.heading('#0', text='文件夹结构', anchor='w')

        # 滚动条
        tree_scrollbar_v = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.folder_tree.yview)
        tree_scrollbar_h = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.folder_tree.xview)
        self.folder_tree.configure(yscrollcommand=tree_scrollbar_v.set, xscrollcommand=tree_scrollbar_h.set)

        # 布局
        self.folder_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        tree_scrollbar_v.pack(side=tk.RIGHT, fill=tk.Y)
        tree_scrollbar_h.pack(side=tk.BOTTOM, fill=tk.X)

        # 右侧：待移动文件列表
        right_frame = ttk.LabelFrame(main_frame, text="待移动文件")
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))

        # 文件列表
        files_frame = ttk.Frame(right_frame)
        files_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.files_listbox = tk.Listbox(files_frame, selectmode=tk.EXTENDED)

        # 滚动条
        files_scrollbar = ttk.Scrollbar(files_frame, orient=tk.VERTICAL, command=self.files_listbox.yview)
        self.files_listbox.configure(yscrollcommand=files_scrollbar.set)

        # 布局
        self.files_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        files_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 底部按钮
        button_frame = ttk.Frame(self.tree_frame)
        button_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        ttk.Button(button_frame, text="清空文件夹", command=self.tree_clear_folders).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="清空文件", command=self.tree_clear_files).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="移动选中文件", command=self.tree_move_files).pack(side=tk.RIGHT)

        # 绑定事件
        self.folder_tree.bind('<Double-1>', self.on_tree_double_click)
        self.files_listbox.bind('<Double-1>', self.on_file_double_click)

        # 设置拖拽支持
        self.folder_tree.drop_target_register(DND_FILES)
        self.folder_tree.dnd_bind('<<Drop>>', self.on_tree_folder_drop)
        self.files_listbox.drop_target_register(DND_FILES)
        self.files_listbox.dnd_bind('<<Drop>>', self.on_tree_file_drop)

    def on_tree_folder_drop(self, event):
        """处理拖拽到文件夹树的事件"""
        try:
            files = self.parent.root.tk.splitlist(event.data)
            for file_path in files:
                path_obj = Path(file_path)
                if path_obj.exists() and path_obj.is_dir():
                    self.add_folder_to_tree(file_path)
            self.parent.status_label.config(text=f"已添加 {len([f for f in files if Path(f).is_dir()])} 个文件夹", bg="lightgreen")
            self.parent.root.after(2000, lambda: self.parent.status_label.config(text="准备就绪", bg="lightgray"))
        except Exception as e:
            self.parent.status_label.config(text=f"添加文件夹失败: {str(e)}", bg="lightcoral")

    def on_tree_file_drop(self, event):
        """处理拖拽到文件列表的事件"""
        try:
            files = self.parent.root.tk.splitlist(event.data)
            added_count = 0
            start_index = self.files_listbox.size()  # 记录开始添加的位置

            for file_path in files:
                path_obj = Path(file_path)
                if path_obj.exists():
                    # 使用工具函数获取显示名称
                    display_name = get_display_name(file_path)
                    self.files_listbox.insert(tk.END, f"{display_name}|{file_path}")
                    added_count += 1

            # 自动选中新添加的文件
            if added_count > 0:
                self.files_listbox.selection_clear(0, tk.END)  # 清除之前的选择
                for i in range(start_index, start_index + added_count):
                    self.files_listbox.selection_set(i)  # 选中新添加的文件

            self.parent.status_label.config(text=f"已添加并选中 {added_count} 个文件/文件夹", bg="lightgreen")
            self.parent.root.after(2000, lambda: self.parent.status_label.config(text="准备就绪", bg="lightgray"))
        except Exception as e:
            self.parent.status_label.config(text=f"添加文件失败: {str(e)}", bg="lightcoral")

    def add_folder_to_tree(self, folder_path):
        """添加文件夹到树形结构"""
        path_obj = Path(folder_path)
        if not path_obj.exists() or not path_obj.is_dir():
            return

        # 检查是否已存在
        for item in self.tree_folders:
            if item == folder_path:
                return

        self.tree_folders.append(folder_path)

        # 重新构建整个树形结构以确保正确排序
        self.rebuild_folder_tree()

    def rebuild_folder_tree(self):
        """重新构建文件夹树形结构，确保正确排序"""
        # 清空当前树形结构
        self.folder_tree.delete(*self.folder_tree.get_children())

        # 对根级文件夹进行自然排序
        sorted_folders = sorted(self.tree_folders, key=lambda x: natural_sort_key(Path(x).name))

        # 重新添加所有文件夹
        for folder_path in sorted_folders:
            path_obj = Path(folder_path)
            folder_name = path_obj.name
            parent_id = self.folder_tree.insert('', 'end', text=f"📁 {folder_name}",
                                               values=(folder_path,), open=True)

            # 递归添加子文件夹
            self.add_subfolders_to_tree(folder_path, parent_id)

    def add_subfolders_to_tree(self, parent_path, parent_id):
        """递归添加子文件夹"""
        try:
            parent_obj = Path(parent_path)
            # 获取所有子文件夹并按自然排序
            subdirs = [item for item in parent_obj.iterdir() if item.is_dir()]
            subdirs.sort(key=lambda x: natural_sort_key(x.name))  # 使用自然排序

            for item in subdirs:
                folder_name = item.name
                child_id = self.folder_tree.insert(parent_id, 'end',
                                                  text=f"📁 {folder_name}",
                                                  values=(str(item),), open=False)
                # 递归添加子文件夹
                self.add_subfolders_to_tree(str(item), child_id)
        except PermissionError:
            # 跳过无权限访问的文件夹
            pass
        except Exception as e:
            print(f"添加子文件夹时出错: {e}")

    def on_tree_double_click(self, event):
        """双击文件夹树项目"""
        selection = self.folder_tree.selection()
        if selection:
            item = selection[0]
            values = self.folder_tree.item(item, 'values')
            if values:
                folder_path = values[0]
                # 在文件管理器中打开文件夹
                try:
                    os.startfile(folder_path)
                except Exception as e:
                    messagebox.showerror("错误", f"无法打开文件夹: {str(e)}")

    def on_file_double_click(self, event):
        """双击文件列表项目"""
        selection = self.files_listbox.curselection()
        if selection:
            item_text = self.files_listbox.get(selection[0])
            # 从显示文本中提取实际路径
            if '|' in item_text:
                file_path = item_text.split('|', 1)[1]
            else:
                file_path = item_text
            # 在文件管理器中打开文件或文件夹
            try:
                os.startfile(file_path)
            except Exception as e:
                messagebox.showerror("错误", f"无法打开文件: {str(e)}")

    def tree_clear_folders(self):
        """清空文件夹树"""
        self.tree_folders.clear()
        self.folder_tree.delete(*self.folder_tree.get_children())
        self.parent.status_label.config(text="已清空文件夹树", bg="lightblue")
        self.parent.root.after(2000, lambda: self.parent.status_label.config(text="准备就绪", bg="lightgray"))

    def tree_clear_files(self):
        """清空文件列表"""
        self.files_listbox.delete(0, tk.END)
        self.parent.status_label.config(text="已清空文件列表", bg="lightblue")
        self.parent.root.after(2000, lambda: self.parent.status_label.config(text="准备就绪", bg="lightgray"))

    def tree_move_files(self):
        """移动选中的文件到选中的文件夹"""
        # 获取选中的文件夹
        tree_selection = self.folder_tree.selection()
        if not tree_selection:
            messagebox.showwarning("警告", "请先选择目标文件夹")
            return

        target_folder = self.folder_tree.item(tree_selection[0], 'values')[0]
        if not Path(target_folder).exists():
            messagebox.showerror("错误", "目标文件夹不存在")
            return

        # 获取选中的文件
        file_selection = self.files_listbox.curselection()
        if not file_selection:
            messagebox.showwarning("警告", "请先选择要移动的文件")
            return

        # 确认移动
        file_count = len(file_selection)
        if not messagebox.askyesno("确认", f"确定要移动 {file_count} 个文件/文件夹到\n{target_folder} 吗？"):
            return

        success_count = 0
        error_count = 0

        # 从后往前删除，避免索引变化
        for i in reversed(file_selection):
            item_text = self.files_listbox.get(i)
            # 从显示文本中提取实际路径
            if '|' in item_text:
                source_path = item_text.split('|', 1)[1]
            else:
                source_path = item_text
            source_obj = Path(source_path)

            try:
                if source_obj.exists():
                    target_path = Path(target_folder) / source_obj.name

                    # 如果目标已存在，询问是否覆盖
                    if target_path.exists():
                        if not messagebox.askyesno("文件已存在",
                                                 f"目标位置已存在 {source_obj.name}\n是否覆盖？"):
                            continue
                        if target_path.is_dir():
                            shutil.rmtree(target_path)
                        else:
                            target_path.unlink()

                    # 移动文件/文件夹
                    shutil.move(str(source_obj), str(target_path))
                    self.files_listbox.delete(i)
                    success_count += 1
                else:
                    error_count += 1

            except Exception as e:
                error_count += 1
                print(f"移动文件失败: {e}")

        # 显示结果
        if error_count == 0:
            messagebox.showinfo("成功", f"成功移动 {success_count} 个文件/文件夹")
            self.parent.status_label.config(text=f"成功移动 {success_count} 个文件", bg="lightgreen")
        else:
            messagebox.showwarning("部分成功", f"成功移动 {success_count} 个文件/文件夹\n失败 {error_count} 个文件/文件夹")
            self.parent.status_label.config(text=f"移动完成，{error_count} 个失败", bg="orange")

        self.parent.root.after(3000, lambda: self.parent.status_label.config(text="准备就绪", bg="lightgray"))
