import time
import requests
import win32gui
import win32con
import sounddevice as sd
import numpy as np
import threading
from collections import deque

# 配置
WINDOW_KEYWORD = "消息"
DINGDING_WEBHOOK = "https://oapi.dingtalk.com/robot/send?access_token=ee5b869e1a3c55cc799af160c17a9ec3f76e9073eb98ab83c35ac5ddf128e527"
SECURITY_KEYWORD = "来消息了"

CHECK_INTERVAL = 5
ALERT_INTERVAL = 60

# 全局变量
last_alert_time = 0
sound_events = deque(maxlen=100)
sound_monitoring = True

def send_dingding_msg():
    """发送钉钉消息"""
    msg = f"{SECURITY_KEYWORD}：检测到{WINDOW_KEYWORD}有新消息，请及时查看电脑。"
    print(f"🚀 准备发送钉钉消息: {msg}")
    try:
        response = requests.post(DINGDING_WEBHOOK, json={
            "msgtype": "text",
            "text": {"content": msg}
        }, timeout=5)
        
        if response.status_code == 200 and response.json().get("errcode") == 0:
            print("✅ 钉钉消息发送成功")
            return True
        else:
            print(f"❌ 钉钉消息发送失败: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 发送钉钉消息时出错: {e}")
        return False

def get_all_windows():
    """获取所有相关窗口的详细信息"""
    windows = []
    
    def enum_handler(hwnd, _):
        if win32gui.IsWindowVisible(hwnd):
            title = win32gui.GetWindowText(hwnd)
            if WINDOW_KEYWORD in title or "闲管家" in title:
                try:
                    is_minimized = win32gui.IsIconic(hwnd)
                    is_foreground = win32gui.GetForegroundWindow() == hwnd
                    class_name = win32gui.GetClassName(hwnd)
                    
                    windows.append({
                        'hwnd': hwnd,
                        'title': title,
                        'class_name': class_name,
                        'is_minimized': is_minimized,
                        'is_foreground': is_foreground,
                        'is_visible': win32gui.IsWindowVisible(hwnd)
                    })
                except Exception as e:
                    pass
    
    win32gui.EnumWindows(enum_handler, None)
    return windows

def continuous_sound_monitor():
    """持续监听声音 - 调试版本"""
    global sound_events, sound_monitoring
    
    def audio_callback(indata, frames, time_info, status):
        if sound_monitoring:
            volume = np.linalg.norm(indata) / len(indata)
            import time as time_module
            current_time = time_module.time()
            
            # 记录所有声音，不管大小
            sound_events.append({
                'time': current_time,
                'volume': volume
            })
            
            # 只打印比较大的声音
            if volume > 0.01:
                print(f"🔊 声音: 音量={volume:.4f}")
    
    try:
        print("🎧 开始声音监听（调试模式）...")
        with sd.InputStream(callback=audio_callback, channels=1, samplerate=44100, blocksize=1024):
            while sound_monitoring:
                time.sleep(0.1)
    except Exception as e:
        print(f"声音监听异常: {e}")

def analyze_sound_events():
    """分析声音事件"""
    current_time = time.time()
    
    # 最近10秒的声音
    recent_sounds = [e for e in sound_events if current_time - e['time'] < 10]
    
    if not recent_sounds:
        return False, "无声音事件"
    
    # 统计信息
    total_sounds = len(recent_sounds)
    max_volume = max(e['volume'] for e in recent_sounds)
    avg_volume = sum(e['volume'] for e in recent_sounds) / total_sounds
    
    # 检查是否有突出的声音事件
    loud_sounds = [e for e in recent_sounds if e['volume'] > 0.02]
    
    info = f"10秒内{total_sounds}个声音事件, 最大音量{max_volume:.4f}, 平均{avg_volume:.4f}"
    
    if loud_sounds:
        info += f", {len(loud_sounds)}个较大声音"
        return True, info
    
    return False, info

def detailed_window_analysis():
    """详细分析窗口状态"""
    windows = get_all_windows()
    
    print(f"\n📊 找到 {len(windows)} 个相关窗口:")
    print("-" * 80)
    
    target_window = None
    
    for i, window in enumerate(windows, 1):
        status_parts = []
        if window['is_minimized']:
            status_parts.append("最小化")
        if window['is_foreground']:
            status_parts.append("前台")
        if not window['is_minimized'] and not window['is_foreground']:
            status_parts.append("后台")
        
        status = f"[{', '.join(status_parts)}]" if status_parts else "[正常]"
        
        print(f"{i:2d}. {window['title']} {status}")
        print(f"    句柄: {window['hwnd']}, 类名: {window['class_name']}")
        
        # 选择"消息"窗口作为主要监控目标
        if window['title'] == "消息":
            target_window = window
            print(f"    ⭐ 这是主要监控目标")
        
        print()
    
    return target_window

if __name__ == "__main__":
    print("🐛 调试版本 - 闲管家消息监控")
    print("=" * 60)
    print("🎯 目标：找出为什么检测不到声音、闪烁、高亮")
    print("=" * 60)
    
    # 启动声音监听线程
    sound_thread = threading.Thread(target=continuous_sound_monitor, daemon=True)
    sound_thread.start()
    
    print("🔍 开始调试监控...")
    print("请在闲管家中制造一些活动（收发消息），观察检测结果")
    print()
    
    try:
        cycle = 0
        while True:
            cycle += 1
            print(f"\n{'='*20} 检测周期 {cycle} {'='*20}")
            
            # 1. 详细窗口分析
            target_window = detailed_window_analysis()
            
            if not target_window:
                print("❌ 未找到'消息'窗口")
            else:
                print(f"✅ 目标窗口: {target_window['title']}")
                print(f"   状态: 最小化={target_window['is_minimized']}, 前台={target_window['is_foreground']}")
                
                # 只有在最小化时才进行检测
                if target_window['is_minimized']:
                    print("✅ 窗口已最小化，符合监控条件")
                    
                    # 2. 声音事件分析
                    has_sound, sound_info = analyze_sound_events()
                    print(f"🔊 声音分析: {sound_info}")
                    
                    # 3. 简单的触发逻辑
                    if has_sound:
                        current_time = time.time()
                        if current_time - last_alert_time > ALERT_INTERVAL:
                            print("🚨 触发条件满足：窗口最小化 + 检测到声音活动")
                            if send_dingding_msg():
                                last_alert_time = current_time
                        else:
                            remaining = ALERT_INTERVAL - (current_time - last_alert_time)
                            print(f"⏰ 条件满足但防抖中，还需等待 {remaining:.1f} 秒")
                    else:
                        print("🔍 未检测到足够的声音活动")
                        
                else:
                    print("⏸️  窗口未最小化，暂停监控")
            
            print(f"\n⏱️  等待 {CHECK_INTERVAL} 秒后进行下次检测...")
            time.sleep(CHECK_INTERVAL)
            
    except KeyboardInterrupt:
        print("\n✅ 调试监控已停止")
        sound_monitoring = False
