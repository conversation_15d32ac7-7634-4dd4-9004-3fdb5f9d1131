"""
模块化文件/文件夹排序工具 - 主程序
使用模块化架构，包含拖拽排序、批量重命名、树形管理功能
修复了路径显示和文件重新选中问题
"""
import tkinter as tk
from tkinter import ttk
from tkinterdnd2 import TkinterDnD, DND_FILES
from pathlib import Path

from drag_sort import DragSortManager
from batch_rename import BatchRenameManager
from tree_manager import TreeManager
from batch_folder_creator import BatchFolderCreator
from auto_merge_mover import AutoMergeMover


class ModularFileFolderSorter:
    """模块化文件/文件夹排序工具主类"""
    
    def __init__(self, root):
        """初始化应用程序"""
        self.root = root
        self.root.title("模块化文件/文件夹排序工具")
        self.root.geometry("1000x700")
        self.root.attributes('-topmost', True)  # 设置窗口置顶
        
        # 设置样式
        self.setup_styles()
        
        # 创建主界面
        self.setup_main_ui()
        
        # 初始化管理器
        self.setup_managers()
        
        # 设置系统拖拽功能
        self.setup_system_drag_drop()

        # 设置全局键盘事件
        self.setup_global_keyboard_events()
    
    def setup_styles(self):
        """设置界面样式"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # 配置强调按钮样式
        style.configure("Accent.TButton", 
                       background="#0078d4", 
                       foreground="white",
                       font=("Arial", 10, "bold"))
        style.map("Accent.TButton",
                 background=[('active', '#106ebe')])
    
    def setup_main_ui(self):
        """设置主界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建标签页控件
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 创建各个标签页框架
        self.drag_frame = ttk.Frame(self.notebook)
        self.batch_frame = ttk.Frame(self.notebook)
        self.tree_frame = ttk.Frame(self.notebook)
        
        # 添加标签页
        self.notebook.add(self.drag_frame, text="拖拽排序")
        self.notebook.add(self.batch_frame, text="批量重命名")
        self.notebook.add(self.tree_frame, text="树形管理")
        
        # 创建状态栏
        self.setup_status_bar()
    
    def setup_status_bar(self):
        """设置状态栏"""
        status_frame = ttk.Frame(self.root)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM)
        
        self.status_label = tk.Label(status_frame, text="准备就绪", 
                                   relief=tk.SUNKEN, anchor=tk.W,
                                   bg="lightgray", font=("Arial", 9))
        self.status_label.pack(fill=tk.X, padx=2, pady=2)
    
    def setup_managers(self):
        """初始化各个功能管理器"""
        # 状态更新回调函数
        def update_status(message, color="lightgreen"):
            self.status_label.config(text=message, bg=color)
            # 2秒后恢复默认状态
            self.root.after(2000, lambda: self.status_label.config(text="准备就绪", bg="lightgray"))
        
        # 初始化拖拽排序管理器
        self.drag_sort_manager = DragSortManager(self.drag_frame, update_status)
        
        # 初始化批量重命名管理器
        self.batch_rename_manager = BatchRenameManager(self.batch_frame, update_status)
        
        # 初始化树形管理器
        self.tree_manager = TreeManager(self.tree_frame, update_status)

        # 初始化批量文件夹创建管理器
        self.batch_folder_creator = BatchFolderCreator(self.root, update_status)
        self.batch_folder_creator.setup_batch_folder_tab(self.notebook)

        # 初始化自动合并移动管理器
        self.auto_merge_mover = AutoMergeMover(self.root, update_status)
        self.auto_merge_mover.setup_auto_merge_tab(self.notebook)
    
    def setup_system_drag_drop(self):
        """设置系统拖拽功能"""
        self.root.drop_target_register(DND_FILES)
        self.root.dnd_bind('<<Drop>>', self.on_system_drop)
    
    def on_system_drop(self, event):
        """处理从系统拖拽的文件/文件夹"""
        try:
            files = self.root.tk.splitlist(event.data)
            current_tab = self.notebook.select()
            tab_text = self.notebook.tab(current_tab, "text")

            if tab_text == "拖拽排序":
                # 委托给拖拽排序管理器
                self.drag_sort_manager.handle_system_drop(files)
            elif tab_text == "批量重命名":
                # 委托给批量重命名管理器
                self.batch_rename_manager.handle_system_drop(files)
            elif tab_text == "树形管理":
                # 委托给树形管理器
                self.tree_manager.handle_system_drop(files)
            elif tab_text == "批量创建文件夹":
                # 委托给批量文件夹创建管理器
                self.batch_folder_creator.handle_system_drop(files)
            elif tab_text == "自动合并移动":
                # 委托给自动合并移动管理器
                self.auto_merge_mover.handle_system_drop(files)

        except Exception as e:
            self.status_label.config(text=f"拖拽失败: {str(e)}", bg="lightcoral")
            self.root.after(3000, lambda: self.status_label.config(text="准备就绪", bg="lightgray"))

    def setup_global_keyboard_events(self):
        """设置全局键盘事件"""
        self.root.bind('<F5>', self.on_global_f5_refresh)

    def on_global_f5_refresh(self, event=None):
        """全局F5刷新处理"""
        # 获取当前活动的标签页
        current_tab = self.notebook.index(self.notebook.select())
        tab_text = self.notebook.tab(current_tab, "text")

        # 根据当前标签页调用相应的刷新方法
        if tab_text == "拖拽排序":
            self.drag_sort_manager.on_f5_refresh()
        elif tab_text == "批量重命名":
            self.batch_rename_manager.on_f5_refresh()
        elif tab_text == "树形管理":
            self.tree_manager.on_f5_refresh()
        elif tab_text == "批量创建文件夹":
            self.batch_folder_creator.on_f5_refresh()
        elif tab_text == "自动合并移动":
            self.auto_merge_mover.on_f5_refresh()


def main():
    """主函数"""
    root = TkinterDnD.Tk()
    app = ModularFileFolderSorter(root)
    root.mainloop()


if __name__ == "__main__":
    main()
