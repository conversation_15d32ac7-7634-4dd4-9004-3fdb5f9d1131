import time
import ctypes
from pynput import mouse
import pygetwindow as gw

# 指定窗口关键词
TARGET_WINDOW_KEYWORD = "模拟器"  # 可换成微信、浏览器等窗口名称
# TARGET_WINDOW_KEYWORD = "微信读书"  # 可换成微信、浏览器等窗口名称

# 双击间隔
DOUBLE_CLICK_INTERVAL = 0.3
last_click_time = 0

# Windows API常量
SW_HIDE = 0
SW_SHOW = 5
SW_MINIMIZE = 6
SW_RESTORE = 9

user32 = ctypes.windll.user32

# 判断窗口是否可见
def is_window_visible(hwnd):
    return user32.IsWindowVisible(hwnd)

# 切换窗口显示/隐藏状态
def toggle_window():
    windows = gw.getWindowsWithTitle(TARGET_WINDOW_KEYWORD)
    if not windows:
        print(f"未找到包含【{TARGET_WINDOW_KEYWORD}】的窗口")
        return

    win = windows[0]
    hwnd = win._hWnd

    if is_window_visible(hwnd):
        print("窗口可见，隐藏它")
        user32.ShowWindow(hwnd, SW_MINIMIZE)  # 可换成 SW_HIDE 真正隐藏
    else:
        print("窗口不可见，显示它")
        user32.ShowWindow(hwnd, SW_RESTORE)
        user32.SetForegroundWindow(hwnd)

# 鼠标中键双击监听
def on_click(x, y, button, pressed):
    global last_click_time
    if button == mouse.Button.middle and pressed:
        now = time.time()
        # if now - last_click_time <= DOUBLE_CLICK_INTERVAL:
        print("检测到中键单击")
        toggle_window()
        # last_click_time = now

def start_listener():
    with mouse.Listener(on_click=on_click) as listener:
        print("监听中键双击中，按 Ctrl+C 退出")
        listener.join()

if __name__ == "__main__":
    start_listener()