(['D:\\AppData\\SelfSync\\Code\\Python\\Tool\\toolProject\\windows\\Toggle_window.py'],
 ['D:\\AppData\\SelfSync\\Code\\Python\\Tool\\toolProject\\windows'],
 [],
 [('D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\site-packages\\numpy\\_pyinstaller',
   0),
  ('D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [],
 '3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('Toggle_window',
   'D:\\AppData\\SelfSync\\Code\\Python\\Tool\\toolProject\\windows\\Toggle_window.py',
   'PYSOURCE')],
 [('subprocess',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\subprocess.py',
   'PYMODULE'),
  ('selectors',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\selectors.py',
   'PYMODULE'),
  ('contextlib',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\contextlib.py',
   'PYMODULE'),
  ('threading',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\threading.py',
   'PYMODULE'),
  ('_threading_local',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('signal',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\signal.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('gzip',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\gzip.py',
   'PYMODULE'),
  ('argparse',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\argparse.py',
   'PYMODULE'),
  ('textwrap',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\textwrap.py',
   'PYMODULE'),
  ('copy',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\copy.py',
   'PYMODULE'),
  ('gettext',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\gettext.py',
   'PYMODULE'),
  ('_compression',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\_compression.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('ipaddress',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('fnmatch',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\fnmatch.py',
   'PYMODULE'),
  ('getpass',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\getpass.py',
   'PYMODULE'),
  ('nturl2path',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('ftplib',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\ftplib.py',
   'PYMODULE'),
  ('netrc',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\netrc.py',
   'PYMODULE'),
  ('shlex',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\shlex.py',
   'PYMODULE'),
  ('mimetypes',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\mimetypes.py',
   'PYMODULE'),
  ('getopt',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\getopt.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('quopri',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\quopri.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\calendar.py',
   'PYMODULE'),
  ('random',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\random.py',
   'PYMODULE'),
  ('statistics',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\statistics.py',
   'PYMODULE'),
  ('fractions',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\fractions.py',
   'PYMODULE'),
  ('numbers',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\numbers.py',
   'PYMODULE'),
  ('http.cookiejar',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\http\\__init__.py',
   'PYMODULE'),
  ('urllib',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('ssl',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\ssl.py',
   'PYMODULE'),
  ('urllib.response',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('string',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\string.py',
   'PYMODULE'),
  ('hashlib',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\hashlib.py',
   'PYMODULE'),
  ('email',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email.parser',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.message',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.header',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\email\\header.py',
   'PYMODULE'),
  ('bisect',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\bisect.py',
   'PYMODULE'),
  ('xml.sax',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('urllib.parse',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('http.client',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\http\\client.py',
   'PYMODULE'),
  ('decimal',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\contextvars.py',
   'PYMODULE'),
  ('datetime',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\datetime.py',
   'PYMODULE'),
  ('_strptime',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\_strptime.py',
   'PYMODULE'),
  ('base64',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\base64.py',
   'PYMODULE'),
  ('hmac',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\hmac.py',
   'PYMODULE'),
  ('struct',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\struct.py',
   'PYMODULE'),
  ('socket',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\socket.py',
   'PYMODULE'),
  ('tempfile',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\tempfile.py',
   'PYMODULE'),
  ('shutil',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\shutil.py',
   'PYMODULE'),
  ('zipfile',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\zipfile.py',
   'PYMODULE'),
  ('py_compile',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\py_compile.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('typing',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\typing.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib._abc',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('csv',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\csv.py',
   'PYMODULE'),
  ('importlib.readers',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('tokenize',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\tokenize.py',
   'PYMODULE'),
  ('token',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\token.py',
   'PYMODULE'),
  ('pathlib',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\pathlib.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('tarfile',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\tarfile.py',
   'PYMODULE'),
  ('lzma',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\lzma.py',
   'PYMODULE'),
  ('bz2',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\bz2.py',
   'PYMODULE'),
  ('logging',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\pickle.py',
   'PYMODULE'),
  ('pprint',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\pprint.py',
   'PYMODULE'),
  ('dataclasses',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('inspect',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\inspect.py',
   'PYMODULE'),
  ('dis',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\dis.py',
   'PYMODULE'),
  ('opcode',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\opcode.py',
   'PYMODULE'),
  ('ast',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\ast.py',
   'PYMODULE'),
  ('_compat_pickle',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('queue',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\queue.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\secrets.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('runpy',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\runpy.py',
   'PYMODULE'),
  ('pkgutil',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\pkgutil.py',
   'PYMODULE'),
  ('zipimport',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\zipimport.py',
   'PYMODULE'),
  ('multiprocessing',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('tracemalloc',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('stringprep',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\stringprep.py',
   'PYMODULE'),
  ('_py_abc',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\_py_abc.py',
   'PYMODULE'),
  ('pygetwindow',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\site-packages\\pygetwindow\\__init__.py',
   'PYMODULE'),
  ('pygetwindow._pygetwindow_win',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\site-packages\\pygetwindow\\_pygetwindow_win.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('pygetwindow._pygetwindow_macos',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\site-packages\\pygetwindow\\_pygetwindow_macos.py',
   'PYMODULE'),
  ('pyrect',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\site-packages\\pyrect\\__init__.py',
   'PYMODULE'),
  ('doctest',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\doctest.py',
   'PYMODULE'),
  ('unittest',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('asyncio',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('unittest.signals',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.runner',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.case',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest._log',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.result',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.util',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('pdb',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\pdb.py',
   'PYMODULE'),
  ('pydoc',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\pydoc.py',
   'PYMODULE'),
  ('webbrowser',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('http.server',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\http\\server.py',
   'PYMODULE'),
  ('socketserver',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\socketserver.py',
   'PYMODULE'),
  ('html',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\tty.py',
   'PYMODULE'),
  ('sysconfig',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\sysconfig.py',
   'PYMODULE'),
  ('_aix_support',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('platform',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\platform.py',
   'PYMODULE'),
  ('glob',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\glob.py',
   'PYMODULE'),
  ('code',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\code.py',
   'PYMODULE'),
  ('codeop',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\codeop.py',
   'PYMODULE'),
  ('bdb',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\bdb.py',
   'PYMODULE'),
  ('cmd',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\cmd.py',
   'PYMODULE'),
  ('difflib',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\difflib.py',
   'PYMODULE'),
  ('__future__',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\__future__.py',
   'PYMODULE'),
  ('pynput.mouse',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\site-packages\\pynput\\mouse\\__init__.py',
   'PYMODULE'),
  ('pynput._util',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\site-packages\\pynput\\_util\\__init__.py',
   'PYMODULE'),
  ('pynput._util.win32_vks',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\site-packages\\pynput\\_util\\win32_vks.py',
   'PYMODULE'),
  ('six',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\site-packages\\six.py',
   'PYMODULE'),
  ('pynput',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\site-packages\\pynput\\__init__.py',
   'PYMODULE'),
  ('pynput.mouse._xorg',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\site-packages\\pynput\\mouse\\_xorg.py',
   'PYMODULE'),
  ('pynput.mouse._win32',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\site-packages\\pynput\\mouse\\_win32.py',
   'PYMODULE'),
  ('pynput.mouse._dummy',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\site-packages\\pynput\\mouse\\_dummy.py',
   'PYMODULE'),
  ('pynput.mouse._darwin',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\site-packages\\pynput\\mouse\\_darwin.py',
   'PYMODULE'),
  ('pynput.mouse._base',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\site-packages\\pynput\\mouse\\_base.py',
   'PYMODULE'),
  ('pynput.keyboard._xorg',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\site-packages\\pynput\\keyboard\\_xorg.py',
   'PYMODULE'),
  ('pynput.keyboard._win32',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\site-packages\\pynput\\keyboard\\_win32.py',
   'PYMODULE'),
  ('pynput.keyboard._uinput',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\site-packages\\pynput\\keyboard\\_uinput.py',
   'PYMODULE'),
  ('pynput.keyboard._dummy',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\site-packages\\pynput\\keyboard\\_dummy.py',
   'PYMODULE'),
  ('pynput.keyboard._darwin',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\site-packages\\pynput\\keyboard\\_darwin.py',
   'PYMODULE'),
  ('pynput.keyboard._base',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\site-packages\\pynput\\keyboard\\_base.py',
   'PYMODULE'),
  ('pynput._util.xorg_keysyms',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\site-packages\\pynput\\_util\\xorg_keysyms.py',
   'PYMODULE'),
  ('pynput._util.xorg',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\site-packages\\pynput\\_util\\xorg.py',
   'PYMODULE'),
  ('pynput._util.win32',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\site-packages\\pynput\\_util\\win32.py',
   'PYMODULE'),
  ('pynput._util.uinput',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\site-packages\\pynput\\_util\\uinput.py',
   'PYMODULE'),
  ('pynput._util.darwin_vks',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\site-packages\\pynput\\_util\\darwin_vks.py',
   'PYMODULE'),
  ('pynput._util.darwin',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\site-packages\\pynput\\_util\\darwin.py',
   'PYMODULE'),
  ('ctypes.util',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes._aix',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('pynput._info',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\site-packages\\pynput\\_info.py',
   'PYMODULE'),
  ('pynput.keyboard',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\site-packages\\pynput\\keyboard\\__init__.py',
   'PYMODULE'),
  ('ctypes',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\ctypes\\_endian.py',
   'PYMODULE')],
 [('python311.dll',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\python311.dll',
   'BINARY'),
  ('select.pyd',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libssl-3.dll',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY')],
 [],
 [],
 [('base_library.zip',
   'D:\\AppData\\SelfSync\\Code\\Python\\Tool\\toolProject\\windows\\build\\Toggle_window\\base_library.zip',
   'DATA')])
