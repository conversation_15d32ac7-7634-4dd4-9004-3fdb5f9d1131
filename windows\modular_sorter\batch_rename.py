"""
批量重命名管理器
处理文件的批量重命名功能
"""
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from pathlib import Path
from utils import natural_sort_key, remove_number_prefix


class BatchRenameManager:
    """批量重命名管理器"""
    
    def __init__(self, parent_frame, status_callback=None):
        """
        初始化批量重命名管理器
        
        Args:
            parent_frame: 父框架
            status_callback: 状态更新回调函数
        """
        self.parent_frame = parent_frame
        self.status_callback = status_callback
        
        # 数据存储
        self.batch_items = []
        
        # UI组件
        self.batch_text = None
        self.batch_remove_prefix_var = None
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.parent_frame, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 配置权重
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="批量文件重命名工具", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 10))
        
        # 左侧按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=1, column=0, sticky=(tk.W, tk.N), padx=(0, 10))
        
        # 添加文件按钮
        ttk.Button(button_frame, text="添加文件", command=self.batch_add_files).pack(fill=tk.X, pady=2)
        
        # 清空列表按钮
        ttk.Button(button_frame, text="清空列表", command=self.batch_clear_list).pack(fill=tk.X, pady=2)
        
        # 排序选项
        self.batch_remove_prefix_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(
            button_frame, 
            text="排序时去除开头序号", 
            variable=self.batch_remove_prefix_var,
            command=self.batch_sort_files
        ).pack(fill=tk.X, pady=2)
        
        # 分隔线
        ttk.Separator(button_frame, orient='horizontal').pack(fill=tk.X, pady=10)
        
        # 复制到剪贴板按钮
        ttk.Button(button_frame, text="复制文件名", command=self.copy_filenames).pack(fill=tk.X, pady=2)
        
        # 从剪贴板粘贴按钮
        ttk.Button(button_frame, text="粘贴文件名", command=self.paste_filenames).pack(fill=tk.X, pady=2)
        
        # 分隔线
        ttk.Separator(button_frame, orient='horizontal').pack(fill=tk.X, pady=10)
        
        # 保存按钮
        ttk.Button(button_frame, text="保存重命名", command=self.batch_save_changes,
                  style="Accent.TButton").pack(fill=tk.X, pady=2)
        
        # 右侧文本编辑区域
        text_frame = ttk.Frame(main_frame)
        text_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        text_frame.columnconfigure(0, weight=1)
        text_frame.rowconfigure(0, weight=1)
        
        # 文本编辑器
        self.batch_text = tk.Text(text_frame, wrap=tk.NONE, font=("Consolas", 10))
        
        # 滚动条
        text_scrollbar_v = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.batch_text.yview)
        text_scrollbar_h = ttk.Scrollbar(text_frame, orient=tk.HORIZONTAL, command=self.batch_text.xview)
        self.batch_text.configure(yscrollcommand=text_scrollbar_v.set, xscrollcommand=text_scrollbar_h.set)
        
        # 布局
        self.batch_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        text_scrollbar_v.grid(row=0, column=1, sticky=(tk.N, tk.S))
        text_scrollbar_h.grid(row=1, column=0, sticky=(tk.W, tk.E))

        # 设置键盘事件
        self.batch_text.bind('<F5>', self.on_f5_refresh)
        self.batch_text.focus_set()  # 确保可以接收键盘事件
        
        # 说明标签
        info_label = ttk.Label(main_frame, text="拖拽文件到此页面，编辑右侧文件名列表，然后保存", 
                              font=("Arial", 10))
        info_label.grid(row=2, column=0, columnspan=2, pady=(10, 0))

    def batch_add_files(self):
        """添加文件到批量重命名列表"""
        files = filedialog.askopenfilenames(title="选择要重命名的文件")
        if files:
            for file_path in files:
                path_obj = Path(file_path)
                if path_obj.exists():
                    self.batch_items.append({
                        'path': file_path,
                        'original_name': path_obj.name,
                        'new_name': path_obj.name
                    })
            self.batch_sort_files()
            self.update_batch_text()
            self._update_status(f"已添加 {len(files)} 个文件")

    def batch_clear_list(self):
        """清空批量重命名列表"""
        self.batch_items.clear()
        self.batch_text.delete(1.0, tk.END)
        self._update_status("已清空文件列表")

    def batch_sort_files(self):
        """排序批量重命名的文件"""
        if self.batch_remove_prefix_var.get():
            self.batch_items.sort(key=lambda x: natural_sort_key(remove_number_prefix(x['original_name'])))
        else:
            self.batch_items.sort(key=lambda x: natural_sort_key(x['original_name']))

        # 添加序号
        for i, item in enumerate(self.batch_items, 1):
            clean_name = remove_number_prefix(item['original_name'])
            item['new_name'] = f"{i}. {clean_name}"

        self.update_batch_text()

    def update_batch_text(self):
        """更新文本编辑器内容"""
        self.batch_text.delete(1.0, tk.END)
        for item in self.batch_items:
            self.batch_text.insert(tk.END, item['new_name'] + '\n')

    def copy_filenames(self):
        """复制文件名到剪贴板"""
        if not self.batch_items:
            messagebox.showwarning("警告", "没有文件可复制")
            return

        filenames = '\n'.join(item['new_name'] for item in self.batch_items)
        # 获取根窗口来访问剪贴板
        root = self.parent_frame.winfo_toplevel()
        root.clipboard_clear()
        root.clipboard_append(filenames)
        messagebox.showinfo("成功", f"已复制 {len(self.batch_items)} 个文件名到剪贴板")
        self._update_status(f"已复制 {len(self.batch_items)} 个文件名到剪贴板")

    def paste_filenames(self):
        """从剪贴板粘贴文件名"""
        try:
            # 获取根窗口来访问剪贴板
            root = self.parent_frame.winfo_toplevel()
            clipboard_content = root.clipboard_get()
            lines = [line.strip() for line in clipboard_content.split('\n') if line.strip()]

            if len(lines) != len(self.batch_items):
                messagebox.showerror("错误", f"文件名数量不匹配！\n当前文件数：{len(self.batch_items)}\n粘贴的文件名数：{len(lines)}")
                return

            # 更新文件名
            for i, line in enumerate(lines):
                if i < len(self.batch_items):
                    self.batch_items[i]['new_name'] = line

            self.update_batch_text()
            messagebox.showinfo("成功", f"已更新 {len(lines)} 个文件名")
            self._update_status(f"已更新 {len(lines)} 个文件名")

        except tk.TclError:
            messagebox.showerror("错误", "剪贴板为空或无法读取")

    def batch_save_changes(self):
        """保存批量重命名更改"""
        if not self.batch_items:
            messagebox.showwarning("警告", "没有文件需要重命名")
            return

        # 获取文本编辑器中的内容
        text_content = self.batch_text.get(1.0, tk.END).strip()
        lines = [line.strip() for line in text_content.split('\n') if line.strip()]

        if len(lines) != len(self.batch_items):
            messagebox.showerror("错误", f"文件名数量不匹配！\n当前文件数：{len(self.batch_items)}\n编辑器中的文件名数：{len(lines)}")
            return

        # 更新文件名
        for i, line in enumerate(lines):
            if i < len(self.batch_items):
                self.batch_items[i]['new_name'] = line

        # 执行重命名
        success_count = 0
        error_count = 0

        for item in self.batch_items:
            try:
                old_path = Path(item['path'])
                new_path = old_path.parent / item['new_name']

                if old_path != new_path:
                    old_path.rename(new_path)
                    success_count += 1

            except Exception as e:
                error_count += 1
                print(f"重命名失败: {item['original_name']} -> {item['new_name']}, 错误: {e}")

        # 显示结果
        if error_count == 0:
            messagebox.showinfo("成功", f"成功重命名 {success_count} 个文件")
            self.batch_clear_list()
            self._update_status(f"成功重命名 {success_count} 个文件")
        else:
            messagebox.showwarning("部分成功", f"成功重命名 {success_count} 个文件\n失败 {error_count} 个文件")
            self._update_status(f"重命名完成，{error_count} 个失败", "orange")
    
    def handle_system_drop(self, files):
        """处理系统拖拽的文件 - 清空之前的数据，加载新数据"""
        # 清空之前的数据
        self.batch_items.clear()

        for file_path in files:
            path_obj = Path(file_path)
            if path_obj.exists() and path_obj.is_file():
                self.batch_items.append({
                    'path': file_path,
                    'original_name': path_obj.name,
                    'new_name': path_obj.name
                })
        self.batch_sort_files()
        self.update_batch_text()
        self._update_status(f"已清空并重新加载 {len(files)} 个文件到批量重命名")

    def on_f5_refresh(self, event=None):
        """F5刷新列表中的文件"""
        if not self.batch_items:
            self._update_status("列表为空，无需刷新", "lightblue")
            return

        # 检查文件是否仍然存在，移除不存在的项目
        valid_items = []
        removed_count = 0

        for item in self.batch_items:
            path_obj = Path(item['path'])
            if path_obj.exists() and path_obj.is_file():
                # 更新显示名称（可能文件名已改变）
                item['original_name'] = path_obj.name
                item['new_name'] = path_obj.name  # 重置新名称为当前名称
                valid_items.append(item)
            else:
                removed_count += 1

        self.batch_items = valid_items

        # 重新排序和显示
        if self.batch_items:
            self.batch_sort_files()
            self.update_batch_text()

        if removed_count > 0:
            self._update_status(f"已刷新列表，移除了 {removed_count} 个不存在的文件", "orange")
        else:
            self._update_status("已刷新列表，所有文件有效", "lightblue")

    def _update_status(self, message, color="lightgreen"):
        """更新状态信息"""
        if self.status_callback:
            self.status_callback(message, color)
