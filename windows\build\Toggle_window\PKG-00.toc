('D:\\AppData\\SelfSync\\Code\\Python\\Tool\\toolProject\\windows\\build\\Toggle_window\\Toggle_window.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'D:\\AppData\\SelfSync\\Code\\Python\\Tool\\toolProject\\windows\\build\\Toggle_window\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'D:\\AppData\\SelfSync\\Code\\Python\\Tool\\toolProject\\windows\\build\\Toggle_window\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'D:\\AppData\\SelfSync\\Code\\Python\\Tool\\toolProject\\windows\\build\\Toggle_window\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'D:\\AppData\\SelfSync\\Code\\Python\\Tool\\toolProject\\windows\\build\\Toggle_window\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'D:\\AppData\\SelfSync\\Code\\Python\\Tool\\toolProject\\windows\\build\\Toggle_window\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'D:\\AppData\\SelfSync\\Code\\Python\\Tool\\toolProject\\windows\\build\\Toggle_window\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('Toggle_window',
   'D:\\AppData\\SelfSync\\Code\\Python\\Tool\\toolProject\\windows\\Toggle_window.py',
   'PYSOURCE'),
  ('python311.dll',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\python311.dll',
   'BINARY'),
  ('select.pyd',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libssl-3.dll',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'D:\\Programming\\Productivity\\Python\\Python3.11.9\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'D:\\Programming\\Work\\JDK1.8\\jdk1.8.0_181\\jre\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('base_library.zip',
   'D:\\AppData\\SelfSync\\Code\\Python\\Tool\\toolProject\\windows\\build\\Toggle_window\\base_library.zip',
   'DATA')],
 'python311.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
