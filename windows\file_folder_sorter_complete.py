#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件/文件夹拖拽排序工具 - 完整版本
支持拖拽文件和文件夹，自动按数字前缀排序，可重新排列顺序并保存
新增批量重命名功能
"""

import tkinter as tk
from tkinter import ttk
from pathlib import Path
from tkinterdnd2 import DND_FILES, TkinterDnD

# 导入模块化组件
from .drag_sort import DragSortManager
from .batch_rename import BatchRenameManager
from .tree_manager import TreeManager

class FileFolderSorterComplete:
    def __init__(self, root):
        self.root = root
        self.root.title("文件/文件夹排序工具")
        self.root.geometry("800x600")  # 减小窗口宽度
        self.root.attributes('-topmost', True)  # 设置窗口置顶

        # 初始化管理器
        self.drag_sort_manager = DragSortManager(self)
        self.batch_rename_manager = BatchRenameManager(self)
        self.tree_manager = TreeManager(self)

        self.setup_ui()
        self.setup_system_drag_drop()

    def setup_ui(self):
        """设置用户界面"""
        # 创建标签页控件
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建各个标签页
        self.drag_sort_manager.setup_drag_sort_tab(self.notebook)
        self.batch_rename_manager.setup_batch_rename_tab(self.notebook)
        self.tree_manager.setup_tree_manager_tab(self.notebook)

        # 状态栏
        self.status_label = tk.Label(self.root, text="准备就绪",
                                   bg="lightgray", fg="black",
                                   relief="sunken", anchor="w")
        self.status_label.pack(side=tk.BOTTOM, fill=tk.X)









    # 树形管理功能方法
    def on_tree_folder_drop(self, event):
        """处理拖拽到文件夹树的事件"""
        try:
            files = self.root.tk.splitlist(event.data)
            for file_path in files:
                path_obj = Path(file_path)
                if path_obj.exists() and path_obj.is_dir():
                    self.add_folder_to_tree(file_path)
            self.status_label.config(text=f"已添加 {len([f for f in files if Path(f).is_dir()])} 个文件夹", bg="lightgreen")
            self.root.after(2000, lambda: self.status_label.config(text="准备就绪", bg="lightgray"))
        except Exception as e:
            self.status_label.config(text=f"添加文件夹失败: {str(e)}", bg="lightcoral")

    def on_tree_file_drop(self, event):
        """处理拖拽到文件列表的事件"""
        try:
            files = self.root.tk.splitlist(event.data)
            added_count = 0
            start_index = self.files_listbox.size()  # 记录开始添加的位置

            for file_path in files:
                path_obj = Path(file_path)
                if path_obj.exists():
                    # 添加到文件列表，显示完整路径
                    self.files_listbox.insert(tk.END, file_path)
                    added_count += 1

            # 自动选中新添加的文件
            if added_count > 0:
                self.files_listbox.selection_clear(0, tk.END)  # 清除之前的选择
                for i in range(start_index, start_index + added_count):
                    self.files_listbox.selection_set(i)  # 选中新添加的文件

            self.status_label.config(text=f"已添加并选中 {added_count} 个文件/文件夹", bg="lightgreen")
            self.root.after(2000, lambda: self.status_label.config(text="准备就绪", bg="lightgray"))
        except Exception as e:
            self.status_label.config(text=f"添加文件失败: {str(e)}", bg="lightcoral")

    def add_folder_to_tree(self, folder_path):
        """添加文件夹到树形结构"""
        path_obj = Path(folder_path)
        if not path_obj.exists() or not path_obj.is_dir():
            return

        # 检查是否已存在
        for item in self.tree_folders:
            if item == folder_path:
                return

        self.tree_folders.append(folder_path)

        # 重新构建整个树形结构以确保正确排序
        self.rebuild_folder_tree()

    def rebuild_folder_tree(self):
        """重新构建文件夹树形结构，确保正确排序"""
        # 清空当前树形结构
        self.folder_tree.delete(*self.folder_tree.get_children())

        # 对根级文件夹进行自然排序
        sorted_folders = sorted(self.tree_folders, key=lambda x: natural_sort_key(Path(x).name))

        # 重新添加所有文件夹
        for folder_path in sorted_folders:
            path_obj = Path(folder_path)
            folder_name = path_obj.name
            parent_id = self.folder_tree.insert('', 'end', text=f"📁 {folder_name}",
                                               values=(folder_path,), open=True)

            # 递归添加子文件夹
            self.add_subfolders_to_tree(folder_path, parent_id)

    def add_subfolders_to_tree(self, parent_path, parent_id):
        """递归添加子文件夹"""
        try:
            parent_obj = Path(parent_path)
            # 获取所有子文件夹并按自然排序
            subdirs = [item for item in parent_obj.iterdir() if item.is_dir()]
            subdirs.sort(key=lambda x: natural_sort_key(x.name))  # 使用自然排序

            for item in subdirs:
                folder_name = item.name
                child_id = self.folder_tree.insert(parent_id, 'end',
                                                  text=f"📁 {folder_name}",
                                                  values=(str(item),), open=False)
                # 递归添加子文件夹
                self.add_subfolders_to_tree(str(item), child_id)
        except PermissionError:
            # 跳过无权限访问的文件夹
            pass
        except Exception as e:
            print(f"添加子文件夹时出错: {e}")

    def on_tree_double_click(self, event):
        """双击文件夹树项目"""
        selection = self.folder_tree.selection()
        if selection:
            item = selection[0]
            values = self.folder_tree.item(item, 'values')
            if values:
                folder_path = values[0]
                # 在文件管理器中打开文件夹
                try:
                    os.startfile(folder_path)
                except Exception as e:
                    messagebox.showerror("错误", f"无法打开文件夹: {str(e)}")

    def on_file_double_click(self, event):
        """双击文件列表项目"""
        selection = self.files_listbox.curselection()
        if selection:
            file_path = self.files_listbox.get(selection[0])
            # 在文件管理器中打开文件或文件夹
            try:
                os.startfile(file_path)
            except Exception as e:
                messagebox.showerror("错误", f"无法打开文件: {str(e)}")

    def tree_clear_folders(self):
        """清空文件夹树"""
        self.tree_folders.clear()
        self.folder_tree.delete(*self.folder_tree.get_children())
        self.status_label.config(text="已清空文件夹树", bg="lightblue")
        self.root.after(2000, lambda: self.status_label.config(text="准备就绪", bg="lightgray"))

    def tree_clear_files(self):
        """清空文件列表"""
        self.files_listbox.delete(0, tk.END)
        self.status_label.config(text="已清空文件列表", bg="lightblue")
        self.root.after(2000, lambda: self.status_label.config(text="准备就绪", bg="lightgray"))

    def tree_move_files(self):
        """移动选中的文件到选中的文件夹"""
        # 获取选中的文件夹
        tree_selection = self.folder_tree.selection()
        if not tree_selection:
            messagebox.showwarning("警告", "请先选择目标文件夹")
            return

        target_folder = self.folder_tree.item(tree_selection[0], 'values')[0]
        if not Path(target_folder).exists():
            messagebox.showerror("错误", "目标文件夹不存在")
            return

        # 获取选中的文件
        file_selection = self.files_listbox.curselection()
        if not file_selection:
            messagebox.showwarning("警告", "请先选择要移动的文件")
            return

        # 确认移动
        file_count = len(file_selection)
        if not messagebox.askyesno("确认", f"确定要移动 {file_count} 个文件/文件夹到\n{target_folder} 吗？"):
            return

        success_count = 0
        error_count = 0

        # 从后往前删除，避免索引变化
        for i in reversed(file_selection):
            source_path = self.files_listbox.get(i)
            source_obj = Path(source_path)

            try:
                if source_obj.exists():
                    target_path = Path(target_folder) / source_obj.name

                    # 如果目标已存在，询问是否覆盖
                    if target_path.exists():
                        if not messagebox.askyesno("文件已存在",
                                                 f"目标位置已存在 {source_obj.name}\n是否覆盖？"):
                            continue
                        if target_path.is_dir():
                            shutil.rmtree(target_path)
                        else:
                            target_path.unlink()

                    # 移动文件/文件夹
                    shutil.move(str(source_obj), str(target_path))
                    self.files_listbox.delete(i)
                    success_count += 1
                else:
                    error_count += 1

            except Exception as e:
                error_count += 1
                print(f"移动文件失败: {e}")

        # 显示结果
        if error_count == 0:
            messagebox.showinfo("成功", f"成功移动 {success_count} 个文件/文件夹")
            self.status_label.config(text=f"成功移动 {success_count} 个文件", bg="lightgreen")
        else:
            messagebox.showwarning("部分成功", f"成功移动 {success_count} 个文件/文件夹\n失败 {error_count} 个文件/文件夹")
            self.status_label.config(text=f"移动完成，{error_count} 个失败", bg="orange")

        self.root.after(3000, lambda: self.status_label.config(text="准备就绪", bg="lightgray"))

    def setup_system_drag_drop(self):
        """设置系统拖拽功能"""
        self.root.drop_target_register(DND_FILES)
        self.root.dnd_bind('<<Drop>>', self.on_system_drop)

    def on_system_drop(self, event):
        """处理从系统拖拽的文件/文件夹"""
        try:
            files = self.root.tk.splitlist(event.data)
            current_tab = self.notebook.select()
            tab_text = self.notebook.tab(current_tab, "text")

            if tab_text == "批量重命名":
                # 添加到批量重命名列表
                for file_path in files:
                    path_obj = Path(file_path)
                    if path_obj.exists() and path_obj.is_file():
                        self.batch_items.append({
                            'path': file_path,
                            'original_name': path_obj.name,
                            'new_name': path_obj.name
                        })
                self.batch_sort_files()
                self.update_batch_text()
                self.status_label.config(text=f"已添加 {len(files)} 个文件到批量重命名", bg="lightgreen")
            elif tab_text == "树形管理":
                # 树形管理功能
                folder_count = 0
                file_count = 0
                start_index = self.files_listbox.size()  # 记录开始添加文件的位置

                for file_path in files:
                    path_obj = Path(file_path)
                    if path_obj.exists():
                        if path_obj.is_dir():
                            self.add_folder_to_tree(file_path)
                            folder_count += 1
                        else:
                            self.files_listbox.insert(tk.END, file_path)
                            file_count += 1

                # 自动选中新添加的文件
                if file_count > 0:
                    self.files_listbox.selection_clear(0, tk.END)  # 清除之前的选择
                    for i in range(start_index, start_index + file_count):
                        self.files_listbox.selection_set(i)  # 选中新添加的文件

                self.status_label.config(text=f"已添加 {folder_count} 个文件夹，{file_count} 个文件并自动选中", bg="lightgreen")
            else:
                # 拖拽排序功能
                added_any = False
                for file_path in files:
                    if self.add_item(file_path):
                        added_any = True
                if added_any:
                    self.auto_reorder_items()
                    self.status_label.config(text=f"已添加 {len(files)} 个项目", bg="lightgreen")

            # 2秒后恢复状态栏
            self.root.after(2000, lambda: self.status_label.config(text="准备就绪", bg="lightgray"))

        except Exception as e:
            self.status_label.config(text=f"拖拽失败: {str(e)}", bg="lightcoral")

def main():
    root = TkinterDnD.Tk()
    app = FileFolderSorterComplete(root)
    root.mainloop()

if __name__ == "__main__":
    main()
