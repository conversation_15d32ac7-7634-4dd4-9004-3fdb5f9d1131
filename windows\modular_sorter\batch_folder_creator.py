#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量文件夹创建管理器
支持在选中的多个文件夹下批量创建指定名称的子文件夹
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from pathlib import Path
import shutil
from utils import natural_sort_key, remove_number_prefix, get_display_name


class BatchFolderCreator:
    """批量文件夹创建管理器"""
    
    def __init__(self, parent_frame, status_callback=None):
        """初始化批量文件夹创建管理器"""
        self.parent_frame = parent_frame
        self.status_callback = status_callback
        self.target_folders = []  # 存储目标文件夹路径
        
    def setup_batch_folder_tab(self, notebook):
        """设置批量文件夹创建标签页"""
        # 创建标签页
        self.tab_frame = ttk.Frame(notebook)
        notebook.add(self.tab_frame, text="批量创建文件夹")
        
        # 创建主要布局
        main_paned = ttk.PanedWindow(self.tab_frame, orient=tk.HORIZONTAL)
        main_paned.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧面板 - 目标文件夹列表
        left_frame = ttk.LabelFrame(main_paned, text="目标文件夹列表", padding=10)
        main_paned.add(left_frame, weight=1)
        
        # 右侧面板 - 要创建的文件夹名称
        right_frame = ttk.LabelFrame(main_paned, text="要创建的文件夹名称", padding=10)
        main_paned.add(right_frame, weight=1)
        
        self.setup_left_panel(left_frame)
        self.setup_right_panel(right_frame)
        
        # 底部操作按钮
        self.setup_bottom_buttons()
        
    def setup_left_panel(self, parent):
        """设置左侧面板"""
        # 操作按钮
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(button_frame, text="添加文件夹", command=self.add_folders).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="清空列表", command=self.clear_folder_list).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="移除选中", command=self.remove_selected_folders).pack(side=tk.LEFT)
        
        # 文件夹列表
        list_frame = ttk.Frame(parent)
        list_frame.pack(fill=tk.BOTH, expand=True)
        
        # 配置grid权重
        list_frame.grid_rowconfigure(0, weight=1)
        list_frame.grid_columnconfigure(0, weight=1)
        
        self.folder_listbox = tk.Listbox(list_frame, selectmode=tk.EXTENDED)
        self.folder_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 滚动条
        folder_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.folder_listbox.yview)
        self.folder_listbox.configure(yscrollcommand=folder_scrollbar.set)
        folder_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 设置拖拽支持
        self.folder_listbox.drop_target_register('DND_Files')
        self.folder_listbox.dnd_bind('<<Drop>>', self.on_folder_drop)

        # 设置键盘事件
        self.folder_listbox.bind('<F5>', self.on_f5_refresh)
        self.folder_listbox.focus_set()  # 确保可以接收键盘事件
        
    def setup_right_panel(self, parent):
        """设置右侧面板"""
        # 说明标签
        info_label = ttk.Label(parent, text="每行一个文件夹名称，将在左侧选中的文件夹下创建这些子文件夹：")
        info_label.pack(anchor=tk.W, pady=(0, 10))
        
        # 文本编辑区域
        text_frame = ttk.Frame(parent)
        text_frame.pack(fill=tk.BOTH, expand=True)
        
        # 配置grid权重
        text_frame.grid_rowconfigure(0, weight=1)
        text_frame.grid_columnconfigure(0, weight=1)
        
        self.folder_names_text = tk.Text(text_frame, wrap=tk.WORD, font=("Consolas", 10))
        self.folder_names_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 滚动条
        text_scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.folder_names_text.yview)
        self.folder_names_text.configure(yscrollcommand=text_scrollbar.set)
        text_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 示例文本
        example_text = """示例：
新建文件夹1
新建文件夹2
项目资料
临时文件
备份"""
        self.folder_names_text.insert(tk.END, example_text)
        
    def setup_bottom_buttons(self):
        """设置底部按钮"""
        button_frame = ttk.Frame(self.tab_frame)
        button_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        ttk.Button(button_frame, text="批量创建文件夹", command=self.batch_create_folders, 
                  style="Accent.TButton").pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="预览创建结果", command=self.preview_creation).pack(side=tk.RIGHT)
        
    def add_folders(self):
        """添加文件夹"""
        folders = filedialog.askdirectory(title="选择目标文件夹", mustexist=True)
        if folders:
            self.add_folder_to_list(folders)
            
    def add_folder_to_list(self, folder_path):
        """添加文件夹到列表"""
        path_obj = Path(folder_path)
        if not path_obj.exists() or not path_obj.is_dir():
            return False
            
        # 检查是否已存在
        if folder_path in self.target_folders:
            return False
            
        self.target_folders.append(folder_path)
        
        # 排序并更新显示
        self.sort_and_refresh_folders()
        self._update_status(f"已添加文件夹: {path_obj.name}")
        return True
        
    def sort_and_refresh_folders(self):
        """排序并刷新文件夹列表"""
        # 按文件夹名称排序（去除数字前缀）
        self.target_folders.sort(key=lambda x: natural_sort_key(remove_number_prefix(Path(x).name)))
        
        # 刷新显示
        self.folder_listbox.delete(0, tk.END)
        for folder_path in self.target_folders:
            display_name = get_display_name(folder_path, max_length=50)
            self.folder_listbox.insert(tk.END, display_name)
            
    def clear_folder_list(self):
        """清空文件夹列表"""
        self.target_folders.clear()
        self.folder_listbox.delete(0, tk.END)
        self._update_status("已清空文件夹列表", "lightblue")
        
    def remove_selected_folders(self):
        """移除选中的文件夹"""
        selection = self.folder_listbox.curselection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要移除的文件夹")
            return
            
        # 从后往前删除，避免索引变化
        for i in reversed(selection):
            if 0 <= i < len(self.target_folders):
                removed_folder = self.target_folders.pop(i)
                self.folder_listbox.delete(i)
                
        self._update_status(f"已移除 {len(selection)} 个文件夹")
        
    def on_folder_drop(self, event):
        """处理拖拽文件夹事件"""
        try:
            # 获取根窗口来访问tk.splitlist
            root = self.parent_frame.winfo_toplevel()
            files = root.tk.splitlist(event.data)
            added_count = 0
            
            for file_path in files:
                path_obj = Path(file_path)
                if path_obj.exists() and path_obj.is_dir():
                    if self.add_folder_to_list(file_path):
                        added_count += 1
                        
            if added_count > 0:
                self._update_status(f"已添加 {added_count} 个文件夹")
        except Exception as e:
            self._update_status(f"拖拽失败: {str(e)}", "lightcoral")
            
    def get_folder_names_to_create(self):
        """获取要创建的文件夹名称列表"""
        text_content = self.folder_names_text.get(1.0, tk.END).strip()
        if not text_content:
            return []
            
        # 分割行并过滤空行
        lines = [line.strip() for line in text_content.split('\n') if line.strip()]
        
        # 过滤掉示例文本
        example_lines = {"示例：", "新建文件夹1", "新建文件夹2", "项目资料", "临时文件", "备份"}
        folder_names = [line for line in lines if line not in example_lines]
        
        return folder_names
        
    def preview_creation(self):
        """预览创建结果"""
        selected_folders = self.folder_listbox.curselection()
        if not selected_folders:
            messagebox.showwarning("警告", "请先选择目标文件夹")
            return
            
        folder_names = self.get_folder_names_to_create()
        if not folder_names:
            messagebox.showwarning("警告", "请输入要创建的文件夹名称")
            return
            
        # 构建预览信息
        preview_text = "将要创建的文件夹：\n\n"
        
        for i in selected_folders:
            if 0 <= i < len(self.target_folders):
                target_folder = self.target_folders[i]
                preview_text += f"在 {Path(target_folder).name} 下创建：\n"
                for folder_name in folder_names:
                    preview_text += f"  📁 {folder_name}\n"
                preview_text += "\n"
                
        # 显示预览对话框
        preview_window = tk.Toplevel(self.parent_frame)
        preview_window.title("预览创建结果")
        preview_window.geometry("500x400")
        preview_window.transient(self.parent_frame.winfo_toplevel())
        preview_window.grab_set()
        
        text_widget = tk.Text(preview_window, wrap=tk.WORD, font=("Consolas", 10))
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        text_widget.insert(tk.END, preview_text)
        text_widget.config(state=tk.DISABLED)
        
        ttk.Button(preview_window, text="关闭", command=preview_window.destroy).pack(pady=10)
        
    def batch_create_folders(self):
        """批量创建文件夹"""
        selected_folders = self.folder_listbox.curselection()
        if not selected_folders:
            messagebox.showwarning("警告", "请先选择目标文件夹")
            return
            
        folder_names = self.get_folder_names_to_create()
        if not folder_names:
            messagebox.showwarning("警告", "请输入要创建的文件夹名称")
            return
            
        # 确认创建
        total_folders = len(selected_folders) * len(folder_names)
        if not messagebox.askyesno("确认", f"确定要创建 {total_folders} 个文件夹吗？"):
            return
            
        success_count = 0
        error_count = 0
        error_messages = []
        
        for i in selected_folders:
            if 0 <= i < len(self.target_folders):
                target_folder = Path(self.target_folders[i])
                
                for folder_name in folder_names:
                    try:
                        new_folder_path = target_folder / folder_name
                        new_folder_path.mkdir(exist_ok=True)
                        success_count += 1
                    except Exception as e:
                        error_count += 1
                        error_messages.append(f"{target_folder.name}/{folder_name}: {str(e)}")
                        
        # 显示结果
        if error_count == 0:
            messagebox.showinfo("成功", f"成功创建 {success_count} 个文件夹")
            self._update_status(f"成功创建 {success_count} 个文件夹")
        else:
            error_text = "\n".join(error_messages[:5])  # 只显示前5个错误
            if len(error_messages) > 5:
                error_text += f"\n... 还有 {len(error_messages) - 5} 个错误"
            messagebox.showwarning("部分成功", 
                                 f"成功创建 {success_count} 个文件夹\n失败 {error_count} 个\n\n错误详情：\n{error_text}")
            self._update_status(f"创建完成，{error_count} 个失败", "orange")
            
    def handle_system_drop(self, files):
        """处理系统拖拽的文件 - 清空之前的数据，加载新数据"""
        # 清空之前的数据
        self.clear_folder_list()

        added_count = 0
        for file_path in files:
            path_obj = Path(file_path)
            if path_obj.exists() and path_obj.is_dir():
                if self.add_folder_to_list(file_path):
                    added_count += 1

        if added_count > 0:
            self._update_status(f"已清空并重新加载 {added_count} 个文件夹")

    def on_f5_refresh(self, event=None):
        """F5刷新列表中的文件夹"""
        if not self.target_folders:
            self._update_status("列表为空，无需刷新", "lightblue")
            return

        # 检查文件夹是否仍然存在，移除不存在的文件夹
        valid_folders = []
        removed_count = 0

        for folder_path in self.target_folders:
            path_obj = Path(folder_path)
            if path_obj.exists() and path_obj.is_dir():
                valid_folders.append(folder_path)
            else:
                removed_count += 1

        self.target_folders = valid_folders

        # 重新排序和显示
        if self.target_folders:
            self.sort_and_refresh_folders()

        if removed_count > 0:
            self._update_status(f"已刷新列表，移除了 {removed_count} 个不存在的文件夹", "orange")
        else:
            self._update_status("已刷新列表，所有文件夹有效", "lightblue")

    def _update_status(self, message, color="lightgreen"):
        """更新状态栏"""
        if self.status_callback:
            self.status_callback(message, color)
