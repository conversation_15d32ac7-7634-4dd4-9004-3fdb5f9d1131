import ctypes
from ctypes import wintypes
import time
from pynput import mouse

TARGET_WINDOW_KEYWORD = "微信读书"
HIDE_INSTEAD_OF_MINIMIZE = False  # True=隐藏，False=最小化

user32 = ctypes.WinDLL('user32', use_last_error=True)

EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool, wintypes.HWND, wintypes.LPARAM)
EnumChildProc = ctypes.WINFUNCTYPE(ctypes.c_bool, wintypes.HWND, wintypes.LPARAM)

user32.EnumWindows.restype = wintypes.BOOL
user32.EnumWindows.argtypes = [EnumWindowsProc, wintypes.LPARAM]

user32.EnumChildWindows.restype = wintypes.BOOL
user32.EnumChildWindows.argtypes = [wintypes.HWND, EnumChildProc, wintypes.LPARAM]

user32.GetWindowTextLengthW.restype = ctypes.c_int
user32.GetWindowTextLengthW.argtypes = [wintypes.HWND]

user32.GetWindowTextW.restype = ctypes.c_int
user32.GetWindowTextW.argtypes = [wintypes.HWND, wintypes.LPWSTR, ctypes.c_int]

user32.IsWindowVisible.restype = wintypes.BOOL
user32.IsWindowVisible.argtypes = [wintypes.HWND]

user32.IsIconic.restype = wintypes.BOOL
user32.IsIconic.argtypes = [wintypes.HWND]

user32.ShowWindow.restype = wintypes.BOOL
user32.ShowWindow.argtypes = [wintypes.HWND, ctypes.c_int]

user32.SetForegroundWindow.restype = wintypes.BOOL
user32.SetForegroundWindow.argtypes = [wintypes.HWND]

user32.SetWindowPos.restype = wintypes.BOOL
user32.SetWindowPos.argtypes = [wintypes.HWND, wintypes.HWND,
                                ctypes.c_int, ctypes.c_int, ctypes.c_int, ctypes.c_int, ctypes.c_uint]

user32.BringWindowToTop.restype = wintypes.BOOL
user32.BringWindowToTop.argtypes = [wintypes.HWND]

SW_HIDE = 0
SW_SHOW = 5
SW_MINIMIZE = 6
SW_RESTORE = 9

HWND_TOPMOST = -1
HWND_NOTOPMOST = -2
SWP_NOMOVE = 0x0002
SWP_NOSIZE = 0x0001

def get_window_text(hwnd):
    length = user32.GetWindowTextLengthW(hwnd)
    if length == 0:
        return ""
    buff = ctypes.create_unicode_buffer(length + 1)
    user32.GetWindowTextW(hwnd, buff, length + 1)
    return buff.value

def is_window_match(hwnd, keyword):
    if not user32.IsWindowVisible(hwnd):
        return False
    title = get_window_text(hwnd)
    return keyword in title and len(title.strip()) > 0

def find_windows_recursively(hwnd, keyword, found_list):
    def child_enum_proc(child_hwnd, lParam):
        if is_window_match(child_hwnd, keyword):
            found_list.append(child_hwnd)
            return False
        find_windows_recursively(child_hwnd, keyword, found_list)
        return True
    user32.EnumChildWindows(hwnd, EnumChildProc(child_enum_proc), 0)

def find_target_window(keyword):
    found_windows = []
    def enum_proc(hwnd, lParam):
        if is_window_match(hwnd, keyword):
            found_windows.append(hwnd)
            return False
        find_windows_recursively(hwnd, keyword, found_windows)
        return True
    user32.EnumWindows(EnumWindowsProc(enum_proc), 0)
    return found_windows[0] if found_windows else None

def force_topmost(hwnd):
    # 先激活窗口
    fg = user32.SetForegroundWindow(hwnd)
    print(f"SetForegroundWindow 返回: {fg}")
    time.sleep(0.05)
    # 取消置顶
    ret1 = user32.SetWindowPos(hwnd, HWND_NOTOPMOST, 0,0,0,0, SWP_NOMOVE | SWP_NOSIZE)
    print(f"SetWindowPos NOTOPMOST 返回: {ret1}")
    time.sleep(0.02)
    # 设置置顶
    ret2 = user32.SetWindowPos(hwnd, HWND_TOPMOST, 0,0,0,0, SWP_NOMOVE | SWP_NOSIZE)
    print(f"SetWindowPos TOPMOST 返回: {ret2}")
    time.sleep(0.02)
    top = user32.BringWindowToTop(hwnd)
    print(f"BringWindowToTop 返回: {top}")

def is_visible(hwnd):
    return bool(user32.IsWindowVisible(hwnd))

def is_minimized(hwnd):
    return bool(user32.IsIconic(hwnd))

def toggle_window():
    hwnd = find_target_window(TARGET_WINDOW_KEYWORD)
    if not hwnd:
        print(f"未找到包含【{TARGET_WINDOW_KEYWORD}】的窗口（含子窗口）")
        return

    visible = is_visible(hwnd)
    minimized = is_minimized(hwnd)
    title = get_window_text(hwnd)

    if visible and not minimized:
        if HIDE_INSTEAD_OF_MINIMIZE:
            print(f"窗口『{title}』可见，隐藏它")
            user32.ShowWindow(hwnd, SW_HIDE)
        else:
            print(f"窗口『{title}』可见，最小化它")
            user32.ShowWindow(hwnd, SW_MINIMIZE)
    else:
        print(f"窗口『{title}』隐藏或最小化，恢复显示并置顶")
        user32.ShowWindow(hwnd, SW_RESTORE)
        force_topmost(hwnd)

def initial_setup():
    hwnd = find_target_window(TARGET_WINDOW_KEYWORD)
    if hwnd:
        force_topmost(hwnd)
        title = get_window_text(hwnd)
        print(f"已将窗口『{title}』置顶")
    else:
        print(f"未找到包含【{TARGET_WINDOW_KEYWORD}】的窗口（含子窗口）")

def on_click(x, y, button, pressed):
    if button == mouse.Button.middle and pressed:
        toggle_window()

def main():
    from pynput import mouse
    initial_setup()
    print("监听中键单击，按 Ctrl+C 退出")
    with mouse.Listener(on_click=on_click) as listener:
        listener.join()

if __name__ == "__main__":
    main()
