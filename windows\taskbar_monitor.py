import time
import requests
import win32gui
import win32con
import win32api
import ctypes
from ctypes import wintypes
import threading

# 配置
WINDOW_KEYWORD = "消息"
DINGDING_WEBHOOK = "https://oapi.dingtalk.com/robot/send?access_token=ee5b869e1a3c55cc799af160c17a9ec3f76e9073eb98ab83c35ac5ddf128e527"
SECURITY_KEYWORD = "来消息了"

CHECK_INTERVAL = 10
ALERT_INTERVAL = 60

# 全局变量
last_alert_time = 0
last_highlight_state = False

def send_dingding_msg():
    """发送钉钉消息"""
    msg = f"{SECURITY_KEYWORD}：检测到{WINDOW_KEYWORD}有新消息，请及时查看电脑。"
    print(f"🚀 准备发送钉钉消息: {msg}")
    try:
        response = requests.post(DINGDING_WEBHOOK, json={
            "msgtype": "text",
            "text": {"content": msg}
        }, timeout=5)
        
        if response.status_code == 200 and response.json().get("errcode") == 0:
            print("✅ 钉钉消息发送成功")
            return True
        else:
            print(f"❌ 钉钉消息发送失败: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 发送钉钉消息时出错: {e}")
        return False

def get_target_window():
    """获取目标窗口信息"""
    def enum_handler(hwnd, result):
        if win32gui.IsWindowVisible(hwnd):
            title = win32gui.GetWindowText(hwnd)
            if WINDOW_KEYWORD in title:
                result.append({
                    'hwnd': hwnd,
                    'title': title,
                    'is_minimized': win32gui.IsIconic(hwnd),
                    'is_foreground': win32gui.GetForegroundWindow() == hwnd
                })
    
    windows = []
    win32gui.EnumWindows(enum_handler, windows)
    return windows[0] if windows else None

def check_taskbar_button_state(hwnd):
    """检查任务栏按钮的高亮状态"""
    try:
        # 方法1: 检查窗口是否最小化但仍然"活跃"
        is_minimized = win32gui.IsIconic(hwnd)
        is_visible = win32gui.IsWindowVisible(hwnd)
        is_foreground = win32gui.GetForegroundWindow() == hwnd
        
        # 如果窗口最小化、可见，但不是前台窗口，很可能在任务栏中高亮
        if is_minimized and is_visible and not is_foreground:
            print(f"📍 窗口状态: 最小化={is_minimized}, 可见={is_visible}, 前台={is_foreground}")
            
            # 方法2: 尝试获取窗口的闪烁信息
            try:
                # 使用FlashWindowEx检查窗口是否在闪烁或高亮
                class FLASHWINFO(ctypes.Structure):
                    _fields_ = [
                        ('cbSize', wintypes.UINT),
                        ('hwnd', wintypes.HWND),
                        ('dwFlags', wintypes.DWORD),
                        ('uCount', wintypes.UINT),
                        ('dwTimeout', wintypes.DWORD)
                    ]
                
                # 尝试停止闪烁来检测当前状态
                flash_info = FLASHWINFO()
                flash_info.cbSize = ctypes.sizeof(FLASHWINFO)
                flash_info.hwnd = hwnd
                flash_info.dwFlags = 0  # FLASHW_STOP
                flash_info.uCount = 0
                flash_info.dwTimeout = 0
                
                user32 = ctypes.windll.user32
                result = user32.FlashWindowEx(ctypes.byref(flash_info))
                
                # 如果FlashWindowEx返回True，说明窗口之前在闪烁/高亮
                if result:
                    print("⚡ FlashWindowEx检测到窗口处于高亮/闪烁状态")
                    return True
                    
            except Exception as e:
                print(f"FlashWindowEx检测失败: {e}")
            
            # 方法3: 简单启发式判断
            # 如果窗口最小化但可见，且不是前台，很可能有未读消息
            return True
            
        return False
        
    except Exception as e:
        print(f"检查任务栏按钮状态时出错: {e}")
        return False

def get_window_title_info(hwnd):
    """获取窗口标题的详细信息，寻找未读消息指示"""
    try:
        title = win32gui.GetWindowText(hwnd)
        
        # 检查标题中是否包含数字（可能是未读消息数量）
        import re
        numbers = re.findall(r'\d+', title)
        
        # 检查标题中是否包含常见的未读消息指示符
        unread_indicators = ['(', ')', '[', ']', '•', '●', '★', '!']
        has_indicators = any(indicator in title for indicator in unread_indicators)
        
        return {
            'title': title,
            'has_numbers': len(numbers) > 0,
            'numbers': numbers,
            'has_indicators': has_indicators
        }
        
    except Exception as e:
        print(f"获取窗口标题信息时出错: {e}")
        return None

def comprehensive_highlight_detection(hwnd):
    """综合检测任务栏图标高亮状态"""
    try:
        # 1. 基本状态检查
        is_minimized = win32gui.IsIconic(hwnd)
        is_visible = win32gui.IsWindowVisible(hwnd)
        is_foreground = win32gui.GetForegroundWindow() == hwnd
        
        if not (is_minimized and is_visible and not is_foreground):
            return False, "窗口不在任务栏中"
        
        # 2. 标题分析
        title_info = get_window_title_info(hwnd)
        if title_info:
            if title_info['has_numbers'] or title_info['has_indicators']:
                return True, f"标题包含未读指示: {title_info['title']}"
        
        # 3. 任务栏按钮状态
        if check_taskbar_button_state(hwnd):
            return True, "任务栏按钮处于高亮状态"
        
        return False, "未检测到高亮状态"
        
    except Exception as e:
        return False, f"检测异常: {e}"

if __name__ == "__main__":
    print("🎯 任务栏图标高亮状态监控")
    print("=" * 50)
    print(f"监控窗口关键词: {WINDOW_KEYWORD}")
    print(f"检测间隔: {CHECK_INTERVAL}秒")
    print(f"防抖间隔: {ALERT_INTERVAL}秒")
    print("=" * 50)
    print("🔍 检测策略:")
    print("  - 检查窗口是否在任务栏中最小化")
    print("  - 分析窗口标题中的未读消息指示")
    print("  - 检测任务栏按钮的高亮状态")
    print("按 Ctrl+C 退出...")
    print()
    
    # 检查目标窗口
    window = get_target_window()
    if window:
        print(f"✅ 找到目标窗口: {window['title']} (句柄: {window['hwnd']})")
        
        # 立即检查当前状态
        is_highlighted, reason = comprehensive_highlight_detection(window['hwnd'])
        if is_highlighted:
            print(f"🔥 当前状态: 图标高亮 - {reason}")
        else:
            print(f"💤 当前状态: 图标正常 - {reason}")
    else:
        print(f"❌ 未找到包含'{WINDOW_KEYWORD}'的窗口")
        print("请确保闲管家软件正在运行")
    
    print("\n🔍 开始监控...")
    
    try:
        while True:
            window = get_target_window()
            if not window:
                print("⚠️  目标窗口不存在，跳过本次检测")
                time.sleep(CHECK_INTERVAL)
                continue
            
            # 检测高亮状态
            is_highlighted, reason = comprehensive_highlight_detection(window['hwnd'])
            
            print(f"🔍 检测结果: {'高亮' if is_highlighted else '正常'} - {reason}")
            
            # 状态变化检测
            if is_highlighted and not last_highlight_state:
                print("🆕 检测到图标从正常变为高亮状态！")
                
                current_time = time.time()
                if current_time - last_alert_time > ALERT_INTERVAL:
                    print("🚨 触发通知: 任务栏图标高亮（有未读消息）")
                    if send_dingding_msg():
                        last_alert_time = current_time
                        print(f"✅ 通知已发送，下次通知需等待 {ALERT_INTERVAL} 秒")
                else:
                    remaining = ALERT_INTERVAL - (current_time - last_alert_time)
                    print(f"⏰ 检测到高亮但防抖中，还需等待 {remaining:.1f} 秒")
            
            elif is_highlighted and last_highlight_state:
                print("📍 图标持续高亮中（有未读消息）")
            
            elif not is_highlighted and last_highlight_state:
                print("✅ 图标从高亮变为正常（消息已读）")
            
            # 更新状态
            last_highlight_state = is_highlighted
            
            time.sleep(CHECK_INTERVAL)
            
    except KeyboardInterrupt:
        print("\n✅ 监控已停止")
