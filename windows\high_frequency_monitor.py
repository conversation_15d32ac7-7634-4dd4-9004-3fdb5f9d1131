import time
import requests
import win32gui
import win32con
import win32api
import ctypes
from ctypes import wintypes
from datetime import datetime
import threading
import sounddevice as sd
import numpy as np
from collections import deque

# 配置 - 高频检测
WINDOW_KEYWORD = "消息"
DINGDING_WEBHOOK = "https://oapi.dingtalk.com/robot/send?access_token=ee5b869e1a3c55cc799af160c17a9ec3f76e9073eb98ab83c35ac5ddf128e527"
SECURITY_KEYWORD = "来消息了"

CHECK_INTERVAL = 1  # 1秒检查一次，最高频率
ALERT_INTERVAL = 60  # 1分钟防抖，确保不错过消息

# 全局变量
last_alert_time = 0
sound_events = deque(maxlen=300)
sound_monitoring = True
last_window_title = ""
last_detection_state = {}

def send_dingding_msg(trigger_reasons):
    """发送钉钉消息"""
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    reasons_text = "、".join(trigger_reasons)
    msg = f"{SECURITY_KEYWORD}：{current_time} 检测到{WINDOW_KEYWORD}有新消息({reasons_text})，请尽快查看电脑处理顾客消息！"
    print(f"🚀 发送钉钉消息: {msg}")
    try:
        response = requests.post(DINGDING_WEBHOOK, json={
            "msgtype": "text",
            "text": {"content": msg}
        }, timeout=5)
        
        if response.status_code == 200 and response.json().get("errcode") == 0:
            print("✅ 钉钉消息发送成功")
            return True
        else:
            print(f"❌ 钉钉消息发送失败: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 发送钉钉消息时出错: {e}")
        return False

def get_target_window():
    """获取目标窗口"""
    def enum_handler(hwnd, result):
        try:
            if win32gui.IsWindowVisible(hwnd):
                title = win32gui.GetWindowText(hwnd)
                if title == WINDOW_KEYWORD:
                    result.append({
                        'hwnd': hwnd,
                        'title': title,
                        'is_minimized': win32gui.IsIconic(hwnd),
                        'is_foreground': win32gui.GetForegroundWindow() == hwnd
                    })
        except:
            pass
    
    windows = []
    win32gui.EnumWindows(enum_handler, windows)
    return windows[0] if windows else None

def sound_monitor_thread():
    """高频声音监控线程"""
    global sound_events, sound_monitoring
    
    def audio_callback(indata, frames, time_info, status):
        if sound_monitoring:
            volume = np.linalg.norm(indata) / len(indata)
            current_time = time.time()
            
            sound_events.append({
                'time': current_time,
                'volume': volume
            })
    
    try:
        print("🎧 高频声音监控已启动")
        with sd.InputStream(callback=audio_callback, channels=1, samplerate=44100, blocksize=512):
            while sound_monitoring:
                time.sleep(0.05)  # 更高频率
    except Exception as e:
        print(f"❌ 声音监控异常: {e}")

def check_sound_spike():
    """检测声音突增（消息提示音）"""
    if len(sound_events) < 20:
        return False, "声音数据不足"
    
    current_time = time.time()
    
    # 检查最近2秒的声音
    recent_sounds = [e for e in sound_events if current_time - e['time'] < 2]
    if not recent_sounds:
        return False, "最近2秒无声音"
    
    # 检查最近10秒的基线音量
    baseline_sounds = [e for e in sound_events if 10 > current_time - e['time'] > 2]
    
    if baseline_sounds:
        baseline_avg = sum(e['volume'] for e in baseline_sounds) / len(baseline_sounds)
        recent_max = max(e['volume'] for e in recent_sounds)
        
        # 如果最近音量比基线高很多，可能是提示音
        if recent_max > baseline_avg * 3 and recent_max > 0.02:
            return True, f"声音突增: 当前{recent_max:.4f} vs 基线{baseline_avg:.4f}"
    
    # 检查绝对音量阈值
    recent_max = max(e['volume'] for e in recent_sounds)
    if recent_max > 0.04:
        return True, f"检测到较大音量: {recent_max:.4f}"
    
    return False, f"声音正常: 最大{recent_max:.4f}"

def check_title_change():
    """检测窗口标题变化"""
    global last_window_title
    
    window = get_target_window()
    if not window:
        return False, "未找到窗口"
    
    current_title = window['title']
    
    # 检查标题中的未读指示
    import re
    has_numbers = bool(re.search(r'\d+', current_title))
    has_brackets = any(char in current_title for char in '()[]{}')
    has_symbols = any(char in current_title for char in '•●★!@#*')
    
    if has_numbers or has_brackets or has_symbols:
        return True, f"标题包含指示符: {current_title}"
    
    # 检查标题变化
    if last_window_title and last_window_title != current_title:
        old_title = last_window_title
        last_window_title = current_title
        return True, f"标题变化: {old_title} -> {current_title}"
    
    last_window_title = current_title
    return False, f"标题正常: {current_title}"

def check_window_activity():
    """检测窗口活动状态"""
    window = get_target_window()
    if not window:
        return False, "未找到窗口"
    
    try:
        hwnd = window['hwnd']
        user32 = ctypes.windll.user32
        
        # 检查窗口是否有未处理的消息
        msg_count = user32.GetQueueStatus(0x1FF)
        if msg_count > 0:
            return True, f"窗口有{msg_count}个未处理消息"
        
        # 检查窗口重绘状态
        update_rect = wintypes.RECT()
        has_update = user32.GetUpdateRect(hwnd, ctypes.byref(update_rect), False)
        if has_update:
            return True, "窗口需要重绘（可能有新内容）"
        
        return False, "窗口活动正常"
        
    except Exception as e:
        return False, f"活动检测异常: {e}"

def comprehensive_high_freq_detection():
    """高频综合检测"""
    trigger_reasons = []
    detection_details = []
    
    # 1. 声音突增检测
    sound_result, sound_reason = check_sound_spike()
    if sound_result:
        trigger_reasons.append("声音突增")
    detection_details.append(f"{'✅' if sound_result else '❌'} 声音: {sound_reason}")
    
    # 2. 标题变化检测
    title_result, title_reason = check_title_change()
    if title_result:
        trigger_reasons.append("标题变化")
    detection_details.append(f"{'✅' if title_result else '❌'} 标题: {title_reason}")
    
    # 3. 窗口活动检测
    activity_result, activity_reason = check_window_activity()
    if activity_result:
        trigger_reasons.append("窗口活动")
    detection_details.append(f"{'✅' if activity_result else '❌'} 活动: {activity_reason}")
    
    # 综合判断
    should_trigger = len(trigger_reasons) > 0
    details_text = " | ".join(detection_details)
    
    return should_trigger, trigger_reasons, details_text

if __name__ == "__main__":
    print("⚡ 高频实时检测版本 - 闲管家顾客消息监控")
    print("=" * 70)
    print(f"检查间隔: {CHECK_INTERVAL}秒 (最高频率)")
    print(f"防抖间隔: {ALERT_INTERVAL//60}分钟 (确保不错过)")
    print("=" * 70)
    print("🎯 高频检测策略:")
    print("  1. 声音突增检测 - 识别消息提示音")
    print("  2. 窗口标题变化 - 检测未读指示")
    print("  3. 窗口活动监控 - 检测新内容")
    print("  4. 1秒检查间隔 - 最快响应顾客消息")
    print("  5. 只在人不在电脑边时监控")
    print("按 Ctrl+C 退出...")
    print("=" * 70)
    
    # 启动高频声音监控
    sound_thread = threading.Thread(target=sound_monitor_thread, daemon=True)
    sound_thread.start()
    
    # 检查初始状态
    window = get_target_window()
    if window:
        status = "最小化" if window['is_minimized'] else "显示中"
        print(f"✅ 找到目标窗口: {window['title']} ({status})")
    else:
        print("❌ 未找到'消息'窗口，请确保闲管家正在运行")
    
    print()
    print("⚡ 开始高频实时监控...")
    print("💡 专为快速响应顾客消息而设计")
    print()
    
    try:
        cycle = 0
        while True:
            cycle += 1
            current_time = time.time()
            time_str = datetime.now().strftime("%H:%M:%S")
            
            # 获取窗口状态
            window = get_target_window()
            if not window:
                if cycle % 60 == 1:  # 每分钟提示一次
                    print(f"[{time_str}] ❌ 未找到'消息'窗口")
                time.sleep(CHECK_INTERVAL)
                continue
            
            # 只在人不在电脑边时监控（窗口非前台）
            if window['is_foreground']:
                if cycle % 60 == 1:  # 每分钟提示一次
                    print(f"[{time_str}] 👁️  人在电脑边，暂停监控")
                time.sleep(CHECK_INTERVAL)
                continue
            
            # 高频综合检测
            should_trigger, trigger_reasons, details = comprehensive_high_freq_detection()
            
            # 简化输出 - 只在触发或每60次循环时显示
            if should_trigger or cycle % 60 == 1:
                status = "🚨触发" if should_trigger else "正常"
                print(f"[{time_str}] {status}: {details}")
            
            # 触发通知
            if should_trigger:
                if current_time - last_alert_time > ALERT_INTERVAL:
                    print(f"🚨 立即通知: {', '.join(trigger_reasons)} - 可能有顾客消息！")
                    if send_dingding_msg(trigger_reasons):
                        last_alert_time = current_time
                        print(f"✅ 通知已发送，请尽快查看顾客消息")
                else:
                    remaining = ALERT_INTERVAL - (current_time - last_alert_time)
                    print(f"⏰ 检测到触发但防抖中，{remaining:.0f}秒后可再次通知")
            
            time.sleep(CHECK_INTERVAL)
            
    except KeyboardInterrupt:
        print("\n✅ 高频监控已停止")
        sound_monitoring = False
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        import traceback
        traceback.print_exc()
        sound_monitoring = False
