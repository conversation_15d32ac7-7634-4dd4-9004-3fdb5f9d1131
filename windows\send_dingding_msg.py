import time
import requests
import win32gui
import win32con
import win32api
import win32process
import sounddevice as sd
import numpy as np
import threading
from collections import deque
import ctypes
from ctypes import wintypes

WINDOW_KEYWORD = "消息"  # 根据调试结果找到的窗口关键词
DINGDING_WEBHOOK = "https://oapi.dingtalk.com/robot/send?access_token=ee5b869e1a3c55cc799af160c17a9ec3f76e9073eb98ab83c35ac5ddf128e527"
SECURITY_KEYWORD = "来消息了"  # 钉钉机器人要求的关键词

CHECK_INTERVAL = 5
ALERT_INTERVAL = 60

last_alert_time = 0
window_count_last = 0
window_states = {}  # 存储窗口状态，用于检测变化
sound_events = deque(maxlen=100)  # 存储最近的声音事件
sound_monitoring = True
flash_events = deque(maxlen=50)  # 存储闪烁事件
flash_monitoring = True

# Windows API 常量
FLASHW_STOP = 0
FLASHW_CAPTION = 1
FLASHW_TRAY = 2
FLASHW_ALL = 3
FLASHW_TIMER = 4
FLASHW_TIMERNOFG = 12

def send_dingding_msg():
    msg = f"{SECURITY_KEYWORD}：检测到XGJ有新消息，请及时查看电脑。"
    print(f"准备发送钉钉消息: {msg}")
    try:
        response = requests.post(DINGDING_WEBHOOK, json={
            "msgtype": "text",
            "text": {"content": msg}
        }, timeout=5)
        print(f"钉钉API响应状态码: {response.status_code}")
        print(f"钉钉API响应内容: {response.text}")
        if response.status_code == 200:
            print("✅ 已成功发送钉钉消息")
        else:
            print(f"❌ 钉钉消息发送失败，状态码: {response.status_code}")
    except Exception as e:
        print(f"❌ 发送钉钉消息时出错: {e}")

def get_window_info():
    hwnd_list = []
    def enum_handler(hwnd, _):
        if win32gui.IsWindowVisible(hwnd):
            title = win32gui.GetWindowText(hwnd)
            if WINDOW_KEYWORD in title:
                hwnd_list.append((hwnd, title))
    win32gui.EnumWindows(enum_handler, None)
    return hwnd_list

def flash_window_monitor():
    """监听窗口闪烁事件的后台线程"""
    global flash_events, flash_monitoring

    # 定义FLASHWINFO结构
    class FLASHWINFO(ctypes.Structure):
        _fields_ = [
            ('cbSize', wintypes.UINT),
            ('hwnd', wintypes.HWND),
            ('dwFlags', wintypes.DWORD),
            ('uCount', wintypes.UINT),
            ('dwTimeout', wintypes.DWORD)
        ]

    def check_window_flash_state():
        """检查目标窗口的真实闪烁状态"""
        try:
            windows = get_window_info()
            for hwnd, title in windows:
                # 获取窗口当前状态
                is_minimized = win32gui.IsIconic(hwnd)
                is_visible = win32gui.IsWindowVisible(hwnd)
                foreground = win32gui.GetForegroundWindow()

                # 创建窗口状态键
                window_key = f"{hwnd}_{title}"
                current_time = time.time()

                # 只有当窗口最小化且可见时才进行进一步检查
                if is_minimized and is_visible and hwnd != foreground:
                    # 检查窗口状态是否发生了变化
                    if window_key in window_states:
                        prev_state = window_states[window_key]

                        # 检查是否从非最小化变为最小化（可能表示有新消息）
                        if not prev_state.get('minimized', False) and is_minimized:
                            flash_event = {
                                'time': current_time,
                                'hwnd': hwnd,
                                'title': title,
                                'event_type': 'minimized_with_activity'
                            }
                            flash_events.append(flash_event)
                            print(f"⚡ 检测到窗口最小化事件: {title} (句柄: {hwnd})")

                        # 检查窗口标题是否发生变化（可能表示新消息）
                        if prev_state.get('title', '') != title:
                            flash_event = {
                                'time': current_time,
                                'hwnd': hwnd,
                                'title': title,
                                'event_type': 'title_changed'
                            }
                            flash_events.append(flash_event)
                            print(f"⚡ 检测到窗口标题变化: {title} (句柄: {hwnd})")

                # 更新窗口状态
                window_states[window_key] = {
                    'minimized': is_minimized,
                    'visible': is_visible,
                    'foreground': hwnd == foreground,
                    'title': title,
                    'last_check': current_time
                }

        except Exception as e:
            print(f"检查窗口闪烁状态时出错: {e}")

    print("⚡ 开始监听窗口闪烁...")
    while flash_monitoring:
        check_window_flash_state()
        time.sleep(2)  # 每2秒检查一次，减少频率

def has_recent_flash(seconds=15):
    """检查最近几秒内是否有闪烁事件"""
    current_time = time.time()
    recent_flashes = [event for event in flash_events if current_time - event['time'] < seconds]

    if recent_flashes:
        print(f"⚡ 最近{seconds}秒内检测到 {len(recent_flashes)} 个闪烁事件")
        for event in recent_flashes:
            print(f"   - {event['title']} 在 {current_time - event['time']:.1f}秒前")
        return True
    return False

def detect_window_count_change():
    """简化的窗口数量变化检测"""
    global window_count_last

    windows = get_window_info()
    current_count = len(windows)

    if current_count > window_count_last:
        print(f"✅ 检测到窗口数量增加: {window_count_last} -> {current_count}")
        window_count_last = current_count
        return True

    window_count_last = current_count
    return False

def continuous_sound_monitor():
    """持续监听声音的后台线程"""
    global sound_events, sound_monitoring

    def audio_callback(indata, frames, time_info, status):
        if status:
            print(f"声音监听状态: {status}")

        # 计算音量
        volume = np.linalg.norm(indata) / len(indata)
        import time as time_module
        current_time = time_module.time()

        # 如果音量超过阈值，记录事件
        if volume > 0.02:  # 降低阈值，更敏感
            sound_events.append({
                'time': current_time,
                'volume': volume
            })
            print(f"🔊 检测到声音: 音量={volume:.4f}, 时间={current_time:.1f}")

    try:
        print("🎧 开始持续声音监听...")
        with sd.InputStream(callback=audio_callback, channels=1, samplerate=44100, blocksize=1024):
            while sound_monitoring:
                time.sleep(0.1)
    except Exception as e:
        print(f"声音监听异常: {e}")

def has_recent_sound(seconds=10):
    """检查最近几秒内是否有声音事件"""
    current_time = time.time()
    recent_sounds = [event for event in sound_events if current_time - event['time'] < seconds]

    if recent_sounds:
        print(f"🔊 最近{seconds}秒内检测到 {len(recent_sounds)} 个声音事件")
        return True
    return False

if __name__ == "__main__":
    print("开始后台检测，按 Ctrl+C 退出...")

    # 启动声音监听线程
    sound_thread = threading.Thread(target=continuous_sound_monitor, daemon=True)
    sound_thread.start()

    # 启动闪烁监听线程
    flash_thread = threading.Thread(target=flash_window_monitor, daemon=True)
    flash_thread.start()

    window_count_last = len(get_window_info())

    while True:
        triggered = False

        # 1️⃣ 检查最近的闪烁事件
        if has_recent_flash(15):
            triggered = True
            print("✅ 检测到任务栏闪烁活动")

        # 2️⃣ 窗口数量变化
        if detect_window_count_change():
            triggered = True

        # 3️⃣ 声音检测（检查最近10秒内的声音事件）
        if has_recent_sound(10):
            triggered = True
            print("✅ 检测到最近的声音活动")

        # 推送防抖
        now = time.time()
        if triggered:
            print(f"触发条件满足，检查防抖: 当前时间={now:.1f}, 上次推送时间={last_alert_time:.1f}, 间隔={now - last_alert_time:.1f}秒")
            if now - last_alert_time > ALERT_INTERVAL:
                print("🚀 开始发送钉钉消息...")
                send_dingding_msg()
                last_alert_time = now
            else:
                print(f"⏰ 防抖中，还需等待 {ALERT_INTERVAL - (now - last_alert_time):.1f} 秒")
        else:
            print("🔍 未检测到任何触发条件")

        time.sleep(CHECK_INTERVAL)

    # 停止所有监听线程
    sound_monitoring = False
    flash_monitoring = False
