# 树形管理功能说明

## 功能概述
新增的"树形管理"标签页允许你：
1. 同时拖入多个文件夹，自动展开为树形结构
2. 拖入文件到右侧列表
3. 将文件直接拖拽移动到指定的文件夹层级

## 界面布局
```
┌─────────────────────────────────────────────────────────┐
│                    树形管理标签页                        │
├─────────────────────┬───────────────────────────────────┤
│   文件夹结构        │        待移动文件                 │
│                     │                                   │
│ 📁 项目文件夹       │  C:\Users\<USER>\文件1.txt          │
│   📁 源代码         │  C:\Users\<USER>\文件2.pdf          │
│     📁 模块A        │  C:\Users\<USER>\图片.jpg           │
│     📁 模块B        │                                   │
│   📁 文档           │                                   │
│   📁 测试           │                                   │
│                     │                                   │
└─────────────────────┴───────────────────────────────────┘
│ [清空文件夹] [清空文件]              [移动选中文件] │
└─────────────────────────────────────────────────────────┘
```

## 使用步骤

### 1. 添加文件夹到树形结构
- 将一个或多个文件夹拖拽到左侧"文件夹结构"区域
- 程序会自动展开所有子文件夹，形成完整的树形结构
- 每个文件夹前都有📁图标标识

### 2. 添加待移动文件
- 将文件或文件夹拖拽到右侧"待移动文件"列表
- 支持同时拖拽多个文件
- 列表中显示文件的完整路径

### 3. 移动文件
- 在左侧树形结构中选择目标文件夹
- 在右侧文件列表中选择要移动的文件（支持多选）
- 点击"移动选中文件"按钮
- 确认移动操作

### 4. 其他功能
- **双击文件夹**: 在文件管理器中打开该文件夹
- **双击文件**: 在文件管理器中打开该文件
- **清空文件夹**: 清空左侧的文件夹树
- **清空文件**: 清空右侧的文件列表

## 特色功能

### 🌳 自动树形展开
- 拖入文件夹后自动递归展开所有子文件夹
- 支持多层嵌套的复杂目录结构
- 自动跳过无权限访问的文件夹

### 🎯 精确定位移动
- 可以将文件移动到任意层级的文件夹
- 支持跨多个根文件夹的移动操作
- 移动前会检查目标文件夹是否存在

### 🔄 智能冲突处理
- 如果目标位置已存在同名文件，会询问是否覆盖
- 支持文件和文件夹的覆盖操作
- 移动失败时会显示详细的错误信息

### 📊 实时状态反馈
- 底部状态栏显示操作结果
- 成功/失败数量统计
- 彩色状态提示（绿色=成功，橙色=部分成功，红色=失败）

## 使用场景

### 📁 项目文件整理
```
拖入项目根文件夹 → 展开完整结构 → 将散乱文件移动到对应模块
```

### 📸 照片分类整理
```
拖入年份文件夹 → 展开月份子文件夹 → 将照片移动到对应月份
```

### 📚 文档归档管理
```
拖入文档库文件夹 → 展开分类结构 → 将新文档移动到对应分类
```

## 注意事项
- 移动操作是真实的文件系统移动，请谨慎操作
- 建议在重要文件操作前先备份
- 大文件移动可能需要较长时间
- 跨磁盘移动实际上是复制+删除操作
