import win32gui
import win32con
import time

def get_detailed_window_info():
    """获取详细的窗口信息"""
    windows = []
    
    def enum_handler(hwnd, _):
        if win32gui.IsWindowVisible(hwnd):
            title = win32gui.GetWindowText(hwnd)
            if title.strip():
                try:
                    class_name = win32gui.GetClassName(hwnd)
                    is_minimized = win32gui.IsIconic(hwnd)
                    is_foreground = (win32gui.GetForegroundWindow() == hwnd)
                    
                    # 获取窗口位置
                    rect = win32gui.GetWindowRect(hwnd)
                    
                    # 获取进程信息
                    import win32process
                    _, process_id = win32process.GetWindowThreadProcessId(hwnd)
                    
                    windows.append({
                        'hwnd': hwnd,
                        'title': title,
                        'class_name': class_name,
                        'is_minimized': is_minimized,
                        'is_foreground': is_foreground,
                        'rect': rect,
                        'process_id': process_id
                    })
                except Exception as e:
                    pass
    
    win32gui.EnumWindows(enum_handler, None)
    return windows

def interactive_window_selection():
    """交互式窗口选择"""
    print("🔍 正在扫描所有窗口...")
    windows = get_detailed_window_info()
    
    print(f"\n📊 找到 {len(windows)} 个可见窗口:")
    print("=" * 100)
    
    # 显示所有窗口
    for i, window in enumerate(windows, 1):
        status = []
        if window['is_minimized']:
            status.append("最小化")
        if window['is_foreground']:
            status.append("前台")
        
        status_str = f" [{', '.join(status)}]" if status else ""
        
        print(f"{i:3d}. {window['title']}{status_str}")
        print(f"     类名: {window['class_name']}")
        print(f"     句柄: {window['hwnd']}")
        print(f"     进程ID: {window['process_id']}")
        print()
    
    print("=" * 100)
    print("请按照以下步骤操作:")
    print("1. 打开你的闲管家软件")
    print("2. 确保软件正在运行（可以最小化）")
    print("3. 从上面的列表中找到对应的窗口")
    print("4. 记下窗口的标题关键词")
    print()
    
    # 查找可能的候选窗口
    possible_keywords = ["管家", "客服", "聊天", "消息", "通知", "助手", "服务"]
    candidates = []
    
    for window in windows:
        for keyword in possible_keywords:
            if keyword in window['title']:
                candidates.append((window, keyword))
    
    if candidates:
        print("🎯 可能的候选窗口:")
        for window, keyword in candidates:
            print(f"   - {window['title']} (包含关键词: '{keyword}')")
            print(f"     句柄: {window['hwnd']}, 类名: {window['class_name']}")
        print()
    
    print("💡 建议:")
    print("1. 如果你看到了闲管家相关的窗口，记下它的标题")
    print("2. 在 send_dingding_msg.py 中设置 WINDOW_KEYWORD 为该标题的关键词")
    print("3. 如果没有看到，请确保闲管家软件正在运行")
    
    return windows

def monitor_window_changes():
    """监控窗口变化"""
    print("\n🔄 开始监控窗口变化...")
    print("请操作你的闲管家软件（打开、最小化、收到消息等），观察窗口变化")
    print("按 Ctrl+C 停止监控")
    
    prev_windows = {}
    
    try:
        while True:
            current_windows = {w['hwnd']: w for w in get_detailed_window_info()}
            
            # 检查新窗口
            for hwnd, window in current_windows.items():
                if hwnd not in prev_windows:
                    print(f"🆕 新窗口: {window['title']} (句柄: {hwnd})")
                else:
                    # 检查状态变化
                    prev_window = prev_windows[hwnd]
                    changes = []
                    
                    if prev_window['title'] != window['title']:
                        changes.append(f"标题: '{prev_window['title']}' -> '{window['title']}'")
                    
                    if prev_window['is_minimized'] != window['is_minimized']:
                        changes.append(f"最小化: {prev_window['is_minimized']} -> {window['is_minimized']}")
                    
                    if prev_window['is_foreground'] != window['is_foreground']:
                        changes.append(f"前台: {prev_window['is_foreground']} -> {window['is_foreground']}")
                    
                    if changes:
                        print(f"🔄 窗口变化: {window['title']}")
                        for change in changes:
                            print(f"   - {change}")
            
            # 检查消失的窗口
            for hwnd in prev_windows:
                if hwnd not in current_windows:
                    print(f"❌ 窗口关闭: {prev_windows[hwnd]['title']} (句柄: {hwnd})")
            
            prev_windows = current_windows
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n✅ 监控已停止")

if __name__ == "__main__":
    print("🎯 闲管家窗口识别工具")
    print("=" * 50)
    
    # 显示当前窗口
    interactive_window_selection()
    
    # 询问是否监控变化
    print("\n" + "=" * 50)
    choice = input("是否要监控窗口变化？(y/n): ").lower().strip()
    if choice in ['y', 'yes', '是']:
        monitor_window_changes()
