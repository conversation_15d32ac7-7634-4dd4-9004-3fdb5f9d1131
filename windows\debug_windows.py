import win32gui
import win32con

def list_all_windows():
    """列出所有可见窗口的标题"""
    windows = []
    
    def enum_handler(hwnd, _):
        if win32gui.IsWindowVisible(hwnd):
            title = win32gui.GetWindowText(hwnd)
            if title.strip():  # 只显示有标题的窗口
                class_name = win32gui.GetClassName(hwnd)
                windows.append((hwnd, title, class_name))
    
    win32gui.EnumWindows(enum_handler, None)
    return windows

def find_possible_targets():
    """查找可能的目标窗口"""
    print("🔍 正在扫描所有可见窗口...")
    windows = list_all_windows()
    
    print(f"\n📊 总共找到 {len(windows)} 个可见窗口:")
    print("-" * 80)
    
    possible_targets = []
    
    for i, (hwnd, title, class_name) in enumerate(windows, 1):
        print(f"{i:3d}. 句柄: {hwnd:8d} | 标题: {title}")
        print(f"     类名: {class_name}")
        
        # 检查可能的关键词
        keywords_to_check = ["闲管家", "XGJ", "管家", "客服", "聊天", "消息"]
        for keyword in keywords_to_check:
            if keyword in title:
                possible_targets.append((hwnd, title, keyword))
                print(f"     ✅ 包含关键词: '{keyword}'")
        
        print()
    
    if possible_targets:
        print("🎯 找到可能的目标窗口:")
        for hwnd, title, keyword in possible_targets:
            print(f"   - 窗口: {title}")
            print(f"     关键词: {keyword}")
            print(f"     句柄: {hwnd}")
    else:
        print("❌ 没有找到包含常见关键词的窗口")
        print("\n💡 建议:")
        print("1. 确保目标软件正在运行")
        print("2. 检查软件窗口是否最小化")
        print("3. 从上面的列表中找到正确的窗口标题")

if __name__ == "__main__":
    find_possible_targets()
    
    print("\n" + "="*80)
    print("请根据上面的结果，在 send_dingding_msg.py 中设置正确的 WINDOW_KEYWORD")
    print("例如: WINDOW_KEYWORD = '你找到的窗口标题中的关键词'")
