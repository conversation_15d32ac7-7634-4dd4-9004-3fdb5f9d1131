import time
import requests
import win32gui
import win32con
import win32api
import ctypes
from ctypes import wintypes
from datetime import datetime
import threading
import sounddevice as sd
import numpy as np
from collections import deque

# 配置
WINDOW_KEYWORD = "消息"
DINGDING_WEBHOOK = "https://oapi.dingtalk.com/robot/send?access_token=ee5b869e1a3c55cc799af160c17a9ec3f76e9073eb98ab83c35ac5ddf128e527"
SECURITY_KEYWORD = "来消息了"

CHECK_INTERVAL = 3  # 3秒检查一次，实时性更好
ALERT_INTERVAL = 300  # 5分钟防抖

# 全局变量
last_alert_time = 0
sound_events = deque(maxlen=100)
sound_monitoring = True
last_window_title = ""

def send_dingding_msg(trigger_reasons):
    """发送钉钉消息 - 多条件检测入口"""
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    reasons_text = "、".join(trigger_reasons)
    msg = f"{SECURITY_KEYWORD}：{current_time} 检测到{WINDOW_KEYWORD}有新消息({reasons_text})，请及时查看电脑。"
    print(f"🚀 发送钉钉消息: {msg}")
    try:
        response = requests.post(DINGDING_WEBHOOK, json={
            "msgtype": "text",
            "text": {"content": msg}
        }, timeout=5)
        
        if response.status_code == 200 and response.json().get("errcode") == 0:
            print("✅ 钉钉消息发送成功")
            return True
        else:
            print(f"❌ 钉钉消息发送失败: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 发送钉钉消息时出错: {e}")
        return False

def get_target_window():
    """获取目标窗口"""
    def enum_handler(hwnd, result):
        try:
            if win32gui.IsWindowVisible(hwnd):
                title = win32gui.GetWindowText(hwnd)
                if title == WINDOW_KEYWORD:
                    result.append({
                        'hwnd': hwnd,
                        'title': title,
                        'is_minimized': win32gui.IsIconic(hwnd),
                        'is_foreground': win32gui.GetForegroundWindow() == hwnd
                    })
        except:
            pass
    
    windows = []
    win32gui.EnumWindows(enum_handler, windows)
    return windows[0] if windows else None

def sound_monitor_thread():
    """声音监控线程"""
    global sound_events, sound_monitoring
    
    def audio_callback(indata, frames, time_info, status):
        if sound_monitoring:
            volume = np.linalg.norm(indata) / len(indata)
            current_time = time.time()
            
            sound_events.append({
                'time': current_time,
                'volume': volume
            })
    
    try:
        print("🎧 声音监控已启动")
        with sd.InputStream(callback=audio_callback, channels=1, samplerate=44100, blocksize=1024):
            while sound_monitoring:
                time.sleep(0.1)
    except Exception as e:
        print(f"❌ 声音监控异常: {e}")

def check_sound_activity(seconds=5):
    """检查声音活动 - 更严格的标准"""
    if not sound_events:
        return False, "无声音数据"
    
    current_time = time.time()
    recent_sounds = [e for e in sound_events if current_time - e['time'] < seconds]
    
    if not recent_sounds:
        return False, f"最近{seconds}秒无声音"
    
    # 更严格的声音检测标准
    significant_sounds = [e for e in recent_sounds if e['volume'] > 0.025]  # 提高阈值
    
    if len(significant_sounds) >= 3:  # 需要多个显著声音事件
        max_vol = max(e['volume'] for e in significant_sounds)
        avg_vol = sum(e['volume'] for e in significant_sounds) / len(significant_sounds)
        return True, f"检测到{len(significant_sounds)}个显著声音事件(最大{max_vol:.4f},平均{avg_vol:.4f})"
    
    return False, f"最近{seconds}秒内声音事件不足或音量过小"

def check_title_changes(hwnd):
    """检查窗口标题变化 - 保守检测"""
    global last_window_title
    
    try:
        current_title = win32gui.GetWindowText(hwnd)
        
        # 检查标题中是否有明确的未读消息指示
        import re
        
        # 检查数字（可能是未读消息数）
        numbers = re.findall(r'\((\d+)\)', current_title)  # 括号中的数字
        if numbers:
            return True, f"标题包含未读数量: {current_title}"
        
        # 检查明确的未读指示词
        unread_indicators = ['未读', '新消息', 'new', 'unread', '●', '•', '!']
        if any(indicator in current_title.lower() for indicator in unread_indicators):
            return True, f"标题包含未读指示: {current_title}"
        
        # 检查标题变化
        if last_window_title and last_window_title != current_title:
            last_window_title = current_title
            return True, f"标题发生变化: {last_window_title} -> {current_title}"
        
        last_window_title = current_title
        return False, f"标题正常: {current_title}"
        
    except Exception as e:
        return False, f"标题检测异常: {e}"

def conservative_detection(hwnd):
    """保守检测方法 - 只在有明确证据时触发"""
    trigger_reasons = []
    detection_details = []
    
    # 1. 声音活动检测（提高标准）
    sound_result, sound_reason = check_sound_activity(5)
    if sound_result:
        trigger_reasons.append("声音活动")
    detection_details.append(f"{'✅' if sound_result else '❌'} 声音检测: {sound_reason}")
    
    # 2. 窗口标题检测（更严格）
    title_result, title_reason = check_title_changes(hwnd)
    if title_result:
        trigger_reasons.append("标题变化")
    detection_details.append(f"{'✅' if title_result else '❌'} 标题检测: {title_reason}")
    
    # 综合判断 - 只有明确证据才触发
    should_trigger = len(trigger_reasons) > 0
    details_text = "\n    ".join(detection_details)
    
    return should_trigger, trigger_reasons, details_text

if __name__ == "__main__":
    print("🛡️  保守检测版本 - 闲管家消息监控")
    print("=" * 60)
    print(f"检查间隔: {CHECK_INTERVAL}秒 (实时检测)")
    print(f"防抖间隔: {ALERT_INTERVAL//60}分钟")
    print("=" * 60)
    print("🎯 保守检测策略 (需要明确证据才触发):")
    print("  1. 声音活动检测（提高阈值）")
    print("  2. 窗口标题变化（明确指示符）")
    print("  3. 移除容易误判的检测方法")
    print("  4. 只在窗口非前台时监控")
    print("按 Ctrl+C 退出...")
    print("=" * 60)
    
    # 启动声音监控线程
    sound_thread = threading.Thread(target=sound_monitor_thread, daemon=True)
    sound_thread.start()
    
    # 检查初始状态
    window = get_target_window()
    if window:
        status = "最小化" if window['is_minimized'] else "显示中"
        print(f"✅ 找到目标窗口: {window['title']} ({status})")
        
        if not window['is_foreground']:
            should_trigger, trigger_reasons, details = conservative_detection(window['hwnd'])
            print(f"🔍 初始检测结果: {'触发' if should_trigger else '正常'}")
            if trigger_reasons:
                print(f"    触发原因: {', '.join(trigger_reasons)}")
            print(f"    {details}")
        else:
            print("👁️  窗口当前在前台，暂不检测")
    else:
        print("❌ 未找到'消息'窗口，请确保闲管家正在运行")
    
    print()
    print("🔍 开始保守监控...")
    
    try:
        cycle = 0
        while True:
            cycle += 1
            current_time = time.time()
            time_str = datetime.now().strftime("%H:%M:%S")
            
            # 获取窗口状态
            window = get_target_window()
            if not window:
                print(f"[{time_str}] ❌ 未找到'消息'窗口")
                time.sleep(CHECK_INTERVAL)
                continue
            
            # 只在窗口非前台时进行检测
            if window['is_foreground']:
                if cycle % 20 == 1:  # 每分钟提示一次
                    print(f"[{time_str}] 👁️  窗口在前台显示中，暂停监控")
                time.sleep(CHECK_INTERVAL)
                continue
            
            # 保守检测
            should_trigger, trigger_reasons, details = conservative_detection(window['hwnd'])
            
            # 显示检测结果
            if should_trigger or cycle % 20 == 1:
                status = "触发" if should_trigger else "正常"
                print(f"\n[{time_str}] 🔍 检测结果: {status}")
                if trigger_reasons:
                    print(f"    触发原因: {', '.join(trigger_reasons)}")
                print(f"    {details}")
            
            # 触发通知
            if should_trigger:
                if current_time - last_alert_time > ALERT_INTERVAL:
                    print(f"🚨 触发通知: {', '.join(trigger_reasons)}")
                    if send_dingding_msg(trigger_reasons):
                        last_alert_time = current_time
                        print(f"✅ 通知已发送")
                else:
                    remaining = ALERT_INTERVAL - (current_time - last_alert_time)
                    print(f"⏰ 检测到触发条件但防抖中，还需等待 {remaining/60:.1f} 分钟")
            
            time.sleep(CHECK_INTERVAL)
            
    except KeyboardInterrupt:
        print("\n✅ 保守监控已停止")
        sound_monitoring = False
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        import traceback
        traceback.print_exc()
        sound_monitoring = False
