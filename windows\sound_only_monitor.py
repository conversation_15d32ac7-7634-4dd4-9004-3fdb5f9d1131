import time
import requests
import win32gui
import sounddevice as sd
import numpy as np
from collections import deque
from datetime import datetime
import threading

# 配置
WINDOW_KEYWORD = "消息"
DINGDING_WEBHOOK = "https://oapi.dingtalk.com/robot/send?access_token=ee5b869e1a3c55cc799af160c17a9ec3f76e9073eb98ab83c35ac5ddf128e527"
SECURITY_KEYWORD = "来消息了"

CHECK_INTERVAL = 2  # 2秒检查一次
ALERT_INTERVAL = 300  # 5分钟防抖

# 全局变量
last_alert_time = 0
sound_events = deque(maxlen=200)
sound_monitoring = True

def send_dingding_msg():
    """发送钉钉消息"""
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    msg = f"{SECURITY_KEYWORD}：{current_time} 检测到{WINDOW_KEYWORD}提示音，有新消息，请及时查看电脑。"
    print(f"🚀 发送钉钉消息: {msg}")
    try:
        response = requests.post(DINGDING_WEBHOOK, json={
            "msgtype": "text",
            "text": {"content": msg}
        }, timeout=5)
        
        if response.status_code == 200 and response.json().get("errcode") == 0:
            print("✅ 钉钉消息发送成功")
            return True
        else:
            print(f"❌ 钉钉消息发送失败: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 发送钉钉消息时出错: {e}")
        return False

def get_target_window():
    """获取目标窗口"""
    def enum_handler(hwnd, result):
        try:
            if win32gui.IsWindowVisible(hwnd):
                title = win32gui.GetWindowText(hwnd)
                if title == WINDOW_KEYWORD:
                    result.append({
                        'hwnd': hwnd,
                        'title': title,
                        'is_minimized': win32gui.IsIconic(hwnd),
                        'is_foreground': win32gui.GetForegroundWindow() == hwnd
                    })
        except:
            pass
    
    windows = []
    win32gui.EnumWindows(enum_handler, windows)
    return windows[0] if windows else None

def sound_monitor_thread():
    """声音监控线程"""
    global sound_events, sound_monitoring
    
    def audio_callback(indata, frames, time_info, status):
        if sound_monitoring:
            volume = np.linalg.norm(indata) / len(indata)
            current_time = time.time()
            
            sound_events.append({
                'time': current_time,
                'volume': volume
            })
            
            # 实时显示较大的声音
            if volume > 0.02:
                print(f"🔊 [{datetime.now().strftime('%H:%M:%S')}] 声音: {volume:.4f}")
    
    try:
        print("🎧 声音监控已启动（实时显示音量 > 0.02）")
        with sd.InputStream(callback=audio_callback, channels=1, samplerate=44100, blocksize=1024):
            while sound_monitoring:
                time.sleep(0.1)
    except Exception as e:
        print(f"❌ 声音监控异常: {e}")

def analyze_sound_pattern():
    """分析声音模式，检测消息提示音"""
    if len(sound_events) < 10:
        return False, "声音数据不足"
    
    current_time = time.time()
    
    # 检查最近3秒内的声音活动
    recent_sounds = [e for e in sound_events if current_time - e['time'] < 3]
    
    if not recent_sounds:
        return False, "最近3秒无声音"
    
    # 分析声音特征
    volumes = [e['volume'] for e in recent_sounds]
    max_volume = max(volumes)
    avg_volume = sum(volumes) / len(volumes)
    
    # 检测突然的音量峰值（可能是提示音）
    significant_sounds = [v for v in volumes if v > 0.03]
    
    if len(significant_sounds) >= 2 and max_volume > 0.05:
        return True, f"检测到提示音模式: 峰值{max_volume:.4f}, 平均{avg_volume:.4f}, {len(significant_sounds)}个显著事件"
    
    # 检测连续的中等音量（可能是持续提示音）
    medium_sounds = [v for v in volumes if 0.02 < v < 0.05]
    if len(medium_sounds) >= 5:
        return True, f"检测到持续提示音: {len(medium_sounds)}个中等音量事件"
    
    return False, f"声音正常: 最大{max_volume:.4f}, 平均{avg_volume:.4f}"

if __name__ == "__main__":
    print("🔊 纯声音检测版本 - 闲管家消息监控")
    print("=" * 60)
    print(f"检查间隔: {CHECK_INTERVAL}秒")
    print(f"防抖间隔: {ALERT_INTERVAL//60}分钟")
    print("=" * 60)
    print("🎯 检测策略:")
    print("  1. 只依赖声音检测")
    print("  2. 分析声音模式识别提示音")
    print("  3. 实时显示音量变化")
    print("  4. 只在窗口非前台时监控")
    print("按 Ctrl+C 退出...")
    print("=" * 60)
    
    # 启动声音监控线程
    sound_thread = threading.Thread(target=sound_monitor_thread, daemon=True)
    sound_thread.start()
    
    # 检查初始状态
    window = get_target_window()
    if window:
        status = "最小化" if window['is_minimized'] else "显示中"
        print(f"✅ 找到目标窗口: {window['title']} ({status})")
    else:
        print("❌ 未找到'消息'窗口，请确保闲管家正在运行")
    
    print()
    print("🔍 开始声音监控...")
    print("💡 提示：程序会实时显示音量 > 0.02 的声音事件")
    print("💡 请收发几条消息，观察是否有特定的声音模式")
    print()
    
    try:
        cycle = 0
        while True:
            cycle += 1
            current_time = time.time()
            time_str = datetime.now().strftime("%H:%M:%S")
            
            # 获取窗口状态
            window = get_target_window()
            if not window:
                if cycle % 30 == 1:  # 每分钟提示一次
                    print(f"[{time_str}] ❌ 未找到'消息'窗口")
                time.sleep(CHECK_INTERVAL)
                continue
            
            # 只在窗口非前台时进行检测
            if window['is_foreground']:
                if cycle % 30 == 1:  # 每分钟提示一次
                    print(f"[{time_str}] 👁️  窗口在前台，暂停监控")
                time.sleep(CHECK_INTERVAL)
                continue
            
            # 声音模式分析
            sound_detected, sound_reason = analyze_sound_pattern()
            
            # 每30次循环（1分钟）显示一次状态
            if cycle % 30 == 1:
                print(f"[{time_str}] 🔍 声音分析: {sound_reason}")
            
            # 检测到提示音
            if sound_detected:
                print(f"[{time_str}] 🎵 {sound_reason}")
                
                if current_time - last_alert_time > ALERT_INTERVAL:
                    print("🚨 触发通知: 检测到提示音")
                    if send_dingding_msg():
                        last_alert_time = current_time
                        print(f"✅ 通知已发送")
                else:
                    remaining = ALERT_INTERVAL - (current_time - last_alert_time)
                    print(f"⏰ 检测到提示音但防抖中，还需等待 {remaining/60:.1f} 分钟")
            
            time.sleep(CHECK_INTERVAL)
            
    except KeyboardInterrupt:
        print("\n✅ 声音监控已停止")
        sound_monitoring = False
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        import traceback
        traceback.print_exc()
        sound_monitoring = False
