#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装文件排序工具所需的依赖包
"""

import subprocess
import sys
import os

def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✓ 成功安装 {package}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 安装 {package} 失败: {e}")
        return False

def main():
    print("正在安装文件排序工具所需的依赖包...")
    print("=" * 50)
    
    # 需要安装的包
    packages = [
        "tkinterdnd2"
    ]
    
    success_count = 0
    total_count = len(packages)
    
    for package in packages:
        print(f"正在安装 {package}...")
        if install_package(package):
            success_count += 1
        print()
    
    print("=" * 50)
    print(f"安装完成！成功: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("✓ 所有依赖包安装成功！")
        print("现在可以运行 file_folder_sorter.py 了")
    else:
        print("✗ 部分依赖包安装失败，请检查网络连接或手动安装")
    
    input("按回车键退出...")

if __name__ == "__main__":
    main()
