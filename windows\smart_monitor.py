import time
import requests
import win32gui
import win32con
import sounddevice as sd
import numpy as np
import threading
from collections import deque

# 配置
WINDOW_KEYWORD = "消息"
DINGDING_WEBHOOK = "https://oapi.dingtalk.com/robot/send?access_token=ee5b869e1a3c55cc799af160c17a9ec3f76e9073eb98ab83c35ac5ddf128e527"
SECURITY_KEYWORD = "来消息了"

CHECK_INTERVAL = 3
ALERT_INTERVAL = 60

# 全局变量
last_alert_time = 0
sound_events = deque(maxlen=50)
sound_monitoring = True

def send_dingding_msg():
    """发送钉钉消息"""
    msg = f"{SECURITY_KEYWORD}：检测到{WINDOW_KEYWORD}有新消息，请及时查看电脑。"
    print(f"🚀 准备发送钉钉消息: {msg}")
    try:
        response = requests.post(DINGDING_WEBHOOK, json={
            "msgtype": "text",
            "text": {"content": msg}
        }, timeout=5)
        
        if response.status_code == 200 and response.json().get("errcode") == 0:
            print("✅ 钉钉消息发送成功")
            return True
        else:
            print(f"❌ 钉钉消息发送失败: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 发送钉钉消息时出错: {e}")
        return False

def get_target_window():
    """获取目标窗口信息"""
    def enum_handler(hwnd, result):
        if win32gui.IsWindowVisible(hwnd):
            title = win32gui.GetWindowText(hwnd)
            if WINDOW_KEYWORD in title:
                result.append({
                    'hwnd': hwnd,
                    'title': title,
                    'is_minimized': win32gui.IsIconic(hwnd),
                    'is_foreground': win32gui.GetForegroundWindow() == hwnd
                })
    
    windows = []
    win32gui.EnumWindows(enum_handler, windows)
    return windows[0] if windows else None

def continuous_sound_monitor():
    """持续监听声音"""
    global sound_events, sound_monitoring
    
    def audio_callback(indata, frames, time_info, status):
        if sound_monitoring:
            volume = np.linalg.norm(indata) / len(indata)
            if volume > 0.015:  # 降低阈值，更敏感
                import time as time_module
                current_time = time_module.time()
                sound_events.append({
                    'time': current_time,
                    'volume': volume
                })
                print(f"🔊 检测到声音: 音量={volume:.4f}")
    
    try:
        print("🎧 开始声音监听...")
        with sd.InputStream(callback=audio_callback, channels=1, samplerate=44100, blocksize=1024):
            while sound_monitoring:
                time.sleep(0.1)
    except Exception as e:
        print(f"声音监听异常: {e}")

def has_recent_sound(seconds=8):
    """检查最近是否有声音"""
    current_time = time.time()
    recent_sounds = [e for e in sound_events if current_time - e['time'] < seconds]
    
    if recent_sounds:
        print(f"🔊 最近{seconds}秒内检测到 {len(recent_sounds)} 个声音事件")
        for sound in recent_sounds[-3:]:  # 显示最近3个
            age = current_time - sound['time']
            print(f"   - 音量{sound['volume']:.4f} 在 {age:.1f}秒前")
        return True
    return False

def smart_detection_strategy():
    """智能检测策略：基于声音事件的时间模式"""
    current_time = time.time()
    
    # 获取最近15秒内的声音事件
    recent_sounds = [e for e in sound_events if current_time - e['time'] < 15]
    
    if not recent_sounds:
        return False, "无声音事件"
    
    # 策略1: 检查是否有突发的声音事件（可能是提示音）
    very_recent = [e for e in recent_sounds if current_time - e['time'] < 3]
    if very_recent:
        # 检查音量是否足够大（提示音通常比较响）
        max_volume = max(e['volume'] for e in very_recent)
        if max_volume > 0.03:
            return True, f"检测到强声音事件(音量{max_volume:.4f})"
    
    # 策略2: 检查声音事件的密度
    if len(recent_sounds) >= 2:
        # 如果短时间内有多个声音事件，可能是消息提示
        time_span = max(e['time'] for e in recent_sounds) - min(e['time'] for e in recent_sounds)
        if time_span < 5 and len(recent_sounds) >= 2:
            return True, f"检测到声音事件集群({len(recent_sounds)}个事件在{time_span:.1f}秒内)"
    
    return False, "声音事件不符合消息模式"

if __name__ == "__main__":
    print("🎯 智能闲管家消息监控")
    print("=" * 50)
    print(f"监控窗口关键词: {WINDOW_KEYWORD}")
    print(f"检测间隔: {CHECK_INTERVAL}秒")
    print(f"防抖间隔: {ALERT_INTERVAL}秒")
    print("=" * 50)
    print("🧠 智能检测策略:")
    print("  - 监听声音事件（提示音）")
    print("  - 分析声音模式（音量、时间、密度）")
    print("  - 确认目标窗口存在")
    print("按 Ctrl+C 退出...")
    print()
    
    # 启动声音监听线程
    sound_thread = threading.Thread(target=continuous_sound_monitor, daemon=True)
    sound_thread.start()
    
    # 检查目标窗口
    window = get_target_window()
    if window:
        print(f"✅ 找到目标窗口: {window['title']} (句柄: {window['hwnd']})")
    else:
        print(f"❌ 未找到包含'{WINDOW_KEYWORD}'的窗口")
        print("请确保闲管家软件正在运行")
    
    print("\n🔍 开始智能监控...")
    
    try:
        while True:
            # 确保目标窗口仍然存在
            window = get_target_window()
            if not window:
                print("⚠️  目标窗口不存在，跳过本次检测")
                time.sleep(CHECK_INTERVAL)
                continue
            
            # 使用智能检测策略
            should_alert, reason = smart_detection_strategy()
            
            if should_alert:
                current_time = time.time()
                if current_time - last_alert_time > ALERT_INTERVAL:
                    print(f"🚨 触发智能检测: {reason}")
                    print(f"📱 目标窗口: {window['title']} (最小化: {window['is_minimized']})")
                    
                    if send_dingding_msg():
                        last_alert_time = current_time
                        print(f"✅ 通知已发送，下次通知需等待 {ALERT_INTERVAL} 秒")
                else:
                    remaining = ALERT_INTERVAL - (current_time - last_alert_time)
                    print(f"⏰ 检测到活动但防抖中: {reason}")
                    print(f"   还需等待 {remaining:.1f} 秒")
            else:
                # 只在有声音事件时显示原因，避免刷屏
                if sound_events and time.time() - sound_events[-1]['time'] < 10:
                    print(f"🔍 检测中: {reason}")
            
            time.sleep(CHECK_INTERVAL)
            
    except KeyboardInterrupt:
        print("\n✅ 监控已停止")
        sound_monitoring = False
