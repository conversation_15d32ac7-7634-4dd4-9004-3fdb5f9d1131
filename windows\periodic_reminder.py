import time
import requests
import win32gui
from datetime import datetime

# 配置
WINDOW_KEYWORD = "消息"
DINGDING_WEBHOOK = "https://oapi.dingtalk.com/robot/send?access_token=ee5b869e1a3c55cc799af160c17a9ec3f76e9073eb98ab83c35ac5ddf128e527"
SECURITY_KEYWORD = "来消息了"

# 检查间隔（秒）
CHECK_INTERVAL = 300  # 5分钟
ALERT_INTERVAL = 1800  # 30分钟防抖

# 全局变量
last_alert_time = 0

def send_dingding_msg():
    """发送钉钉消息"""
    current_time = datetime.now().strftime("%H:%M:%S")
    msg = f"{SECURITY_KEYWORD}：{current_time} 检测到{WINDOW_KEYWORD}窗口最小化，可能有未读消息，请及时查看电脑。"
    print(f"🚀 发送钉钉消息: {msg}")
    try:
        response = requests.post(DINGDING_WEBHOOK, json={
            "msgtype": "text",
            "text": {"content": msg}
        }, timeout=5)
        
        if response.status_code == 200 and response.json().get("errcode") == 0:
            print("✅ 钉钉消息发送成功")
            return True
        else:
            print(f"❌ 钉钉消息发送失败: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 发送钉钉消息时出错: {e}")
        return False

def get_target_window():
    """获取目标窗口"""
    def enum_handler(hwnd, result):
        try:
            if win32gui.IsWindowVisible(hwnd):
                title = win32gui.GetWindowText(hwnd)
                if title == WINDOW_KEYWORD:
                    result.append({
                        'hwnd': hwnd,
                        'title': title,
                        'is_minimized': win32gui.IsIconic(hwnd),
                        'is_foreground': win32gui.GetForegroundWindow() == hwnd
                    })
        except:
            pass
    
    windows = []
    win32gui.EnumWindows(enum_handler, windows)
    return windows[0] if windows else None

def format_time_remaining(seconds):
    """格式化剩余时间"""
    if seconds < 60:
        return f"{seconds:.0f}秒"
    elif seconds < 3600:
        return f"{seconds/60:.1f}分钟"
    else:
        return f"{seconds/3600:.1f}小时"

if __name__ == "__main__":
    print("⏰ 定时提醒版本 - 闲管家消息监控")
    print("=" * 50)
    print(f"检查间隔: {CHECK_INTERVAL//60}分钟")
    print(f"提醒间隔: {ALERT_INTERVAL//60}分钟")
    print("=" * 50)
    print("💡 工作原理:")
    print(f"  - 每{CHECK_INTERVAL//60}分钟检查一次'消息'窗口状态")
    print("  - 如果窗口最小化，发送提醒通知")
    print(f"  - 防抖机制：{ALERT_INTERVAL//60}分钟内不重复提醒")
    print("按 Ctrl+C 退出...")
    print("=" * 50)
    
    # 检查初始状态
    window = get_target_window()
    if window:
        status = "最小化" if window['is_minimized'] else "显示中"
        print(f"✅ 找到目标窗口: {window['title']} ({status})")
        if window['is_minimized']:
            print("🔔 窗口当前最小化，将开始定时提醒")
        else:
            print("👁️  窗口当前显示中，暂不提醒")
    else:
        print("❌ 未找到'消息'窗口，请确保闲管家正在运行")
    
    print()
    
    try:
        cycle = 0
        while True:
            cycle += 1
            current_time = time.time()
            time_str = datetime.now().strftime("%H:%M:%S")
            
            print(f"\n[{time_str}] --- 检查周期 {cycle} ---")
            
            # 获取窗口状态
            window = get_target_window()
            if not window:
                print("❌ 未找到'消息'窗口")
                print(f"⏱️  {CHECK_INTERVAL//60}分钟后再次检查...")
                time.sleep(CHECK_INTERVAL)
                continue
            
            print(f"📱 窗口状态: {window['title']} - {'最小化' if window['is_minimized'] else '显示中'}")
            
            # 如果窗口最小化，考虑发送提醒
            if window['is_minimized']:
                if current_time - last_alert_time > ALERT_INTERVAL:
                    print("🚨 窗口最小化，发送提醒通知")
                    if send_dingding_msg():
                        last_alert_time = current_time
                        next_alert_time = ALERT_INTERVAL
                        print(f"✅ 提醒已发送，下次提醒时间: {format_time_remaining(next_alert_time)}后")
                else:
                    remaining = ALERT_INTERVAL - (current_time - last_alert_time)
                    print(f"⏰ 窗口最小化但防抖中，下次提醒: {format_time_remaining(remaining)}后")
            else:
                print("👁️  窗口显示中，无需提醒")
            
            print(f"⏱️  {CHECK_INTERVAL//60}分钟后进行下次检查...")
            time.sleep(CHECK_INTERVAL)
            
    except KeyboardInterrupt:
        print("\n✅ 定时提醒已停止")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        import traceback
        traceback.print_exc()
