#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试树形管理功能
"""

import tkinter as tk
from tkinter import ttk
import os
import tempfile
from pathlib import Path

def test_tree_manager():
    """测试树形管理功能"""
    print("开始测试树形管理功能...")
    
    # 创建临时测试目录结构
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 创建测试文件夹结构
        folders = [
            temp_path / "文件夹1",
            temp_path / "文件夹1" / "子文件夹1",
            temp_path / "文件夹1" / "子文件夹2",
            temp_path / "文件夹2",
            temp_path / "文件夹2" / "子文件夹A",
            temp_path / "文件夹2" / "子文件夹A" / "深层文件夹",
        ]
        
        for folder in folders:
            folder.mkdir(parents=True, exist_ok=True)
            
        # 创建测试文件
        test_files = [
            temp_path / "测试文件1.txt",
            temp_path / "测试文件2.txt",
            temp_path / "文件夹1" / "内部文件.txt",
        ]
        
        for file_path in test_files:
            file_path.write_text("测试内容", encoding='utf-8')
        
        print(f"创建的测试目录: {temp_dir}")
        print("测试目录结构:")
        for root, dirs, files in os.walk(temp_dir):
            level = root.replace(temp_dir, '').count(os.sep)
            indent = ' ' * 2 * level
            print(f"{indent}📁 {os.path.basename(root)}/")
            subindent = ' ' * 2 * (level + 1)
            for file in files:
                print(f"{subindent}📄 {file}")
        
        print("\n✅ 测试目录结构创建成功!")
        print("现在可以启动程序并测试以下功能:")
        print("1. 将文件夹拖拽到左侧树形结构")
        print("2. 将文件拖拽到右侧文件列表")
        print("3. 选择目标文件夹和文件，点击移动按钮")
        print("4. 双击文件夹或文件可以在文件管理器中打开")
        
        # 启动主程序
        try:
            from file_folder_sorter_complete import FileFolderSorterComplete
            from tkinterdnd2 import TkinterDnD
            
            root = TkinterDnD.Tk()
            app = FileFolderSorterComplete(root)
            
            # 切换到树形管理标签页
            app.notebook.select(2)  # 第三个标签页（索引从0开始）
            
            print(f"\n🚀 程序已启动，请在GUI中测试功能")
            print(f"测试目录路径: {temp_dir}")
            
            root.mainloop()
            
        except ImportError as e:
            print(f"❌ 导入错误: {e}")
            print("请确保已安装 tkinterdnd2: pip install tkinterdnd2")
        except Exception as e:
            print(f"❌ 启动错误: {e}")

if __name__ == "__main__":
    test_tree_manager()
