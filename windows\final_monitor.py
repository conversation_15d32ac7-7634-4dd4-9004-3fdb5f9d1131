import time
import requests
import win32gui
import win32con
import threading
from collections import deque

# 配置
WINDOW_KEYWORD = "消息"
DINGDING_WEBHOOK = "https://oapi.dingtalk.com/robot/send?access_token=ee5b869e1a3c55cc799af160c17a9ec3f76e9073eb98ab83c35ac5ddf128e527"
SECURITY_KEYWORD = "来消息了"

CHECK_INTERVAL = 5
ALERT_INTERVAL = 60

# 全局变量
last_alert_time = 0
sound_monitoring = True
sound_events = deque(maxlen=100)

def send_dingding_msg():
    """发送钉钉消息"""
    msg = f"{SECURITY_KEYWORD}：检测到{WINDOW_KEYWORD}有新消息，请及时查看电脑。"
    print(f"🚀 准备发送钉钉消息: {msg}")
    try:
        response = requests.post(DINGDING_WEBHOOK, json={
            "msgtype": "text",
            "text": {"content": msg}
        }, timeout=5)
        
        if response.status_code == 200 and response.json().get("errcode") == 0:
            print("✅ 钉钉消息发送成功")
            return True
        else:
            print(f"❌ 钉钉消息发送失败: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 发送钉钉消息时出错: {e}")
        return False

def get_target_window():
    """获取目标窗口"""
    def enum_handler(hwnd, result):
        try:
            if win32gui.IsWindowVisible(hwnd):
                title = win32gui.GetWindowText(hwnd)
                if title == WINDOW_KEYWORD:  # 精确匹配"消息"
                    result.append({
                        'hwnd': hwnd,
                        'title': title,
                        'is_minimized': win32gui.IsIconic(hwnd),
                        'is_foreground': win32gui.GetForegroundWindow() == hwnd
                    })
        except:
            pass
    
    windows = []
    win32gui.EnumWindows(enum_handler, windows)
    return windows[0] if windows else None

def sound_monitor_thread():
    """声音监控线程 - 使用更简单的方法"""
    global sound_events, sound_monitoring
    
    try:
        import sounddevice as sd
        import numpy as np
        
        def audio_callback(indata, frames, time_info, status):
            if sound_monitoring:
                volume = np.linalg.norm(indata) / len(indata)
                current_time = time.time()
                
                # 记录所有声音事件
                sound_events.append({
                    'time': current_time,
                    'volume': volume
                })
                
                # 只打印较大的声音
                if volume > 0.008:  # 降低阈值
                    print(f"🔊 声音事件: 音量={volume:.4f}")
        
        print("🎧 声音监控已启动")
        with sd.InputStream(callback=audio_callback, channels=1, samplerate=44100, blocksize=1024):
            while sound_monitoring:
                time.sleep(0.1)
                
    except ImportError:
        print("❌ sounddevice未安装，跳过声音监控")
        print("   可以运行: pip install sounddevice")
    except Exception as e:
        print(f"❌ 声音监控异常: {e}")

def analyze_recent_sounds(seconds=8):
    """分析最近的声音事件"""
    if not sound_events:
        return False, "无声音数据"
    
    current_time = time.time()
    recent_sounds = [e for e in sound_events if current_time - e['time'] < seconds]
    
    if not recent_sounds:
        return False, f"最近{seconds}秒无声音"
    
    # 统计
    count = len(recent_sounds)
    max_vol = max(e['volume'] for e in recent_sounds)
    avg_vol = sum(e['volume'] for e in recent_sounds) / count
    
    # 检查是否有明显的声音事件
    significant_sounds = [e for e in recent_sounds if e['volume'] > 0.015]
    
    info = f"{count}个声音事件, 最大音量{max_vol:.4f}, 平均{avg_vol:.4f}"
    
    if significant_sounds:
        info += f", {len(significant_sounds)}个显著声音"
        return True, info
    
    return False, info

def check_window_title_changes(window):
    """检查窗口标题是否包含未读消息指示"""
    title = window['title']
    
    # 检查标题中是否有数字或特殊符号
    import re
    has_numbers = bool(re.search(r'\d+', title))
    has_brackets = any(char in title for char in '()[]{}')
    has_symbols = any(char in title for char in '•●★!@#')
    
    if has_numbers or has_brackets or has_symbols:
        return True, f"标题包含指示符: {title}"
    
    return False, f"标题正常: {title}"

if __name__ == "__main__":
    print("🎯 最终版本 - 闲管家消息监控")
    print("=" * 50)
    print("🔍 检测策略:")
    print("  1. 确保'消息'窗口最小化")
    print("  2. 监听声音事件（提示音）")
    print("  3. 检查窗口标题变化")
    print("  4. 组合判断是否有新消息")
    print("=" * 50)
    
    # 启动声音监控线程
    sound_thread = threading.Thread(target=sound_monitor_thread, daemon=True)
    sound_thread.start()
    
    print("🚀 监控已启动，请在闲管家中收发消息进行测试...")
    print()
    
    try:
        cycle = 0
        while True:
            cycle += 1
            print(f"\n--- 检测周期 {cycle} ---")
            
            # 1. 获取目标窗口
            window = get_target_window()
            if not window:
                print("❌ 未找到'消息'窗口")
                time.sleep(CHECK_INTERVAL)
                continue
            
            # 2. 检查是否最小化
            if not window['is_minimized']:
                print("⏸️  窗口未最小化，暂停监控")
                time.sleep(CHECK_INTERVAL)
                continue
            
            print(f"✅ 目标窗口最小化: {window['title']}")
            
            # 3. 检查声音事件
            has_sound, sound_info = analyze_recent_sounds(8)
            print(f"🔊 声音分析: {sound_info}")
            
            # 4. 检查标题变化
            has_title_change, title_info = check_window_title_changes(window)
            print(f"📝 标题分析: {title_info}")
            
            # 5. 综合判断
            should_alert = False
            reasons = []
            
            if has_sound:
                should_alert = True
                reasons.append("检测到声音活动")
            
            if has_title_change:
                should_alert = True
                reasons.append("标题包含未读指示")
            
            # 6. 发送通知
            if should_alert:
                current_time = time.time()
                if current_time - last_alert_time > ALERT_INTERVAL:
                    print(f"🚨 触发通知: {', '.join(reasons)}")
                    if send_dingding_msg():
                        last_alert_time = current_time
                        print("✅ 通知发送成功")
                else:
                    remaining = ALERT_INTERVAL - (current_time - last_alert_time)
                    print(f"⏰ 条件满足但防抖中，还需等待 {remaining:.1f} 秒")
            else:
                print("🔍 未检测到新消息活动")
            
            time.sleep(CHECK_INTERVAL)
            
    except KeyboardInterrupt:
        print("\n✅ 监控已停止")
        sound_monitoring = False
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        import traceback
        traceback.print_exc()
